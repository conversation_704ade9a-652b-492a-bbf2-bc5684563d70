# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-14
# 描述: 生成可编辑的SVG分子骨架图 - 每个分子都是独立的矢量元素

import csv
import os
import sys
from io import StringIO

# 环境检测和导入
def check_and_import_dependencies():
    """检测并导入必要的依赖包"""
    print("=== 环境依赖检测 ===")
    
    # 检测RDKit
    try:
        from rdkit import Chem
        from rdkit.Chem import Draw
        from rdkit.Chem.Draw import rdMolDraw2D
        print("✓ RDKit导入成功")
        rdkit_available = True
    except ImportError as e:
        print(f"✗ RDKit导入失败: {e}")
        print("请安装RDKit: conda install -c conda-forge rdkit")
        rdkit_available = False
    
    if not rdkit_available:
        print("\n⚠ 缺少必要依赖，程序无法运行")
        return None, None, None
    
    from rdkit import Chem
    from rdkit.Chem import Draw
    from rdkit.Chem.Draw import rdMolDraw2D
    
    print("✓ 所有依赖检测通过\n")
    return Chem, Draw, rdMolDraw2D

# 全局导入
Chem, Draw, rdMolDraw2D = check_and_import_dependencies()
if Chem is None:
    sys.exit(1)

def setup_chinese_academic_style():
    """设置中国风学术配色方案"""
    colors = {
        'primary': '#C0392B',      # 朱红 - 用于强调
        'secondary': '#2980B9',    # 靛蓝 - 用于边框
        'accent1': '#27AE60',      # 石绿 - 用于分类
        'accent2': '#F1C40F',      # 藤黄 - 用于高亮
        'accent3': '#A0522D',      # 赭石 - 用于分组
        'text': '#34495E',         # 青金 - 用于文本
        'background': '#FDFEFE',   # 定窑白 - 背景色
        'molecule': '#000000'      # 黑色 - 分子结构标准色
    }
    
    return colors

def load_scaffold_data():
    """加载分子骨架数据并筛选前10个（排除Scaffold_22）"""
    print("=== 可编辑SVG分子骨架生成器 ===")
    print("加载分子骨架数据...")
    
    data_path = "01_原始数据/分子骨架数据/scaffold_smiles_data.csv"
    
    scaffolds = []
    try:
        with open(data_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                scaffolds.append({
                    'id': row['Scaffold_ID'],
                    'smiles': row['SMILES'],
                    'description': row['Description']
                })
    except FileNotFoundError:
        print(f"✗ 数据文件未找到: {data_path}")
        return []
    except Exception as e:
        print(f"✗ 读取数据文件失败: {str(e)}")
        return []
    
    # 筛选前10个骨架，排除Scaffold_22
    selected_scaffolds = []
    for scaffold in scaffolds:
        if scaffold['id'] != 'Scaffold_22':  # 排除Scaffold_22
            selected_scaffolds.append(scaffold)
        if len(selected_scaffolds) >= 10:  # 只要前10个
            break
    
    print(f"数据加载完成，筛选出 {len(selected_scaffolds)} 个分子骨架（已排除Scaffold_22）")
    return selected_scaffolds

def validate_and_create_molecules(scaffolds):
    """验证SMILES并创建分子对象"""
    print("验证SMILES格式并创建分子对象...")
    
    valid_data = []
    
    for scaffold in scaffolds:
        try:
            mol = Chem.MolFromSmiles(scaffold['smiles'])
            if mol is not None:
                valid_data.append({
                    'mol': mol,
                    'id': scaffold['id'],
                    'smiles': scaffold['smiles'],
                    'description': scaffold['description']
                })
                print(f"  ✓ {scaffold['id']}: 分子对象创建成功")
            else:
                print(f"  ✗ {scaffold['id']}: SMILES格式无效")
        except Exception as e:
            print(f"  ✗ {scaffold['id']}: 处理失败 - {str(e)}")
    
    print(f"成功创建 {len(valid_data)} 个分子对象")
    return valid_data

def generate_molecule_svg(mol, mol_id, size=(350, 350)):
    """生成单个分子的SVG矢量图"""
    try:
        # 使用RDKit的SVG绘制器
        drawer = rdMolDraw2D.MolDraw2DSVG(size[0], size[1])

        # 设置绘制选项（简化设置，避免版本兼容问题）
        opts = drawer.drawOptions()
        opts.addStereoAnnotation = True
        opts.addAtomIndices = False
        opts.bondLineWidth = 2

        # 绘制分子
        drawer.DrawMolecule(mol)
        drawer.FinishDrawing()

        # 获取SVG内容
        svg_content = drawer.GetDrawingText()

        return svg_content
    except Exception as e:
        print(f"生成分子SVG失败 {mol_id}: {str(e)}")
        # 创建错误占位SVG
        error_svg = f'''<svg width="{size[0]}" height="{size[1]}" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="white" stroke="red" stroke-width="2"/>
            <text x="50%" y="50%" text-anchor="middle" fill="red" font-size="16">Error: {mol_id}</text>
        </svg>'''
        return error_svg

def create_editable_svg_layout(valid_data, colors):
    """创建可编辑的SVG布局"""
    print("创建可编辑的SVG布局...")
    
    # 布局参数
    n_cols = 5  # 5列
    n_rows = 2  # 2行
    mol_size = 350  # 每个分子图像大小
    text_height = 90  # 文本区域高度
    margin = 80  # 边距
    spacing = 50  # 分子间距
    
    # 计算画布总尺寸
    canvas_width = margin * 2 + n_cols * mol_size + (n_cols - 1) * spacing
    canvas_height = margin * 2 + n_rows * (mol_size + text_height) + (n_rows - 1) * spacing
    
    # 开始构建SVG
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
     width="{canvas_width}" height="{canvas_height}" viewBox="0 0 {canvas_width} {canvas_height}">
  
  <!-- 样式定义 -->
  <defs>
    <style type="text/css">
      .title-text {{ font-family: Arial, sans-serif; font-size: 32px; font-weight: bold; fill: {colors['text']}; }}
      .id-text {{ font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: {colors['primary']}; }}
      .caption-text {{ font-family: Arial, sans-serif; font-size: 16px; fill: {colors['text']}; }}
      .molecule-border {{ fill: none; stroke: {colors['secondary']}; stroke-width: 3; }}
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="{colors['background']}"/>
  
  <!-- 标题 -->
  <text x="{canvas_width//2}" y="50" text-anchor="middle" class="title-text">
    Representative Molecular Scaffolds for Patent Application
  </text>
  
  <!-- 分子网格 -->
'''
    
    # 添加每个分子
    for i, data in enumerate(valid_data):
        if i >= n_rows * n_cols:  # 最多显示10个
            break
            
        row = i // n_cols
        col = i % n_cols
        
        # 计算位置
        x = margin + col * (mol_size + spacing)
        y = margin + 80 + row * (mol_size + text_height + spacing)  # 80为标题区域
        
        # 生成分子SVG
        mol_svg = generate_molecule_svg(data['mol'], data['id'], (mol_size, mol_size))
        
        # 提取分子SVG的内容（去掉外层svg标签）
        mol_svg_lines = mol_svg.split('\n')
        mol_content = []
        in_svg = False
        for line in mol_svg_lines:
            if '<svg' in line:
                in_svg = True
                continue
            elif '</svg>' in line:
                break
            elif in_svg:
                mol_content.append(line)
        
        mol_inner = '\n'.join(mol_content)
        
        # 添加分子组
        svg_content += f'''
  <!-- 分子 {i+1}: {data['id']} -->
  <g id="molecule_{data['id']}" transform="translate({x}, {y})">
    <!-- 分子边框 -->
    <rect x="-2" y="-2" width="{mol_size+4}" height="{mol_size+4}" class="molecule-border"/>
    
    <!-- 分子结构 -->
    <g id="structure_{data['id']}">
{mol_inner}
    </g>
    
    <!-- 分子ID标签 -->
    <text x="{mol_size//2}" y="{mol_size + 35}" text-anchor="middle" class="id-text" id="label_{data['id']}">
      {data['id']}
    </text>
  </g>
'''
    
    # 添加底部说明
    svg_content += f'''
  
  <!-- 底部说明 -->
  <text x="{canvas_width//2}" y="{canvas_height - 20}" text-anchor="middle" class="caption-text">
    10 Key Molecular Scaffolds | Generated with RDKit | ZK Research Group
  </text>
  
</svg>'''
    
    return svg_content

def save_editable_svg(svg_content, output_dir, filename="分子骨架前10个_可编辑版.svg"):
    """保存可编辑的SVG文件"""
    print("保存可编辑的SVG文件...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    svg_file = os.path.join(output_dir, filename)
    
    try:
        with open(svg_file, 'w', encoding='utf-8') as f:
            f.write(svg_content)
        print(f"✓ 可编辑SVG文件保存完成: {svg_file}")
        
        # 获取文件大小
        file_size = os.path.getsize(svg_file)
        print(f"✓ 文件大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
        
        return svg_file
    except Exception as e:
        print(f"✗ SVG保存失败: {str(e)}")
        return None

def create_svg_usage_guide(svg_file):
    """创建SVG使用指南"""
    guide_file = svg_file.replace('.svg', '_使用指南.txt')
    
    with open(guide_file, 'w', encoding='utf-8') as f:
        f.write("可编辑SVG分子骨架图使用指南\n")
        f.write("=" * 50 + "\n\n")
        f.write("文件特点:\n")
        f.write("✓ 真正的矢量格式，每个分子都是独立的SVG元素\n")
        f.write("✓ 可在Adobe Illustrator、Inkscape等软件中编辑\n")
        f.write("✓ 支持选择、移动、修改单个分子结构\n")
        f.write("✓ 文本标签可独立编辑\n")
        f.write("✓ 颜色、字体、尺寸都可调整\n\n")
        
        f.write("编辑建议:\n")
        f.write("1. Adobe Illustrator:\n")
        f.write("   - 直接打开SVG文件\n")
        f.write("   - 每个分子是一个组，可以取消组合后编辑\n")
        f.write("   - 可修改颜色、线条粗细、字体等\n\n")
        
        f.write("2. Inkscape (免费):\n")
        f.write("   - 开源矢量图编辑软件\n")
        f.write("   - 完全支持SVG格式\n")
        f.write("   - 可精确编辑每个元素\n\n")
        
        f.write("3. 网页浏览器:\n")
        f.write("   - 可直接查看SVG文件\n")
        f.write("   - 支持缩放查看细节\n\n")
        
        f.write("元素结构:\n")
        f.write("- 每个分子包含在独立的<g>组中\n")
        f.write("- ID格式: molecule_Scaffold_X\n")
        f.write("- 结构ID: structure_Scaffold_X\n")
        f.write("- 标签ID: label_Scaffold_X\n")
        f.write("- 可通过ID精确选择和编辑\n\n")
        
        f.write("专利申请建议:\n")
        f.write("- 可根据专利要求调整布局\n")
        f.write("- 可修改标题和说明文字\n")
        f.write("- 可调整分子大小和间距\n")
        f.write("- 可导出为其他格式\n")
    
    print(f"✓ 使用指南已保存: {guide_file}")

def main():
    """主函数"""
    # 设置样式
    colors = setup_chinese_academic_style()
    
    # 加载数据（前10个，排除Scaffold_22）
    scaffolds = load_scaffold_data()
    if not scaffolds:
        print("⚠ 无法加载数据，程序退出")
        return
    
    # 验证SMILES并创建分子对象
    valid_data = validate_and_create_molecules(scaffolds)
    if len(valid_data) == 0:
        print("⚠ 没有有效的分子结构，程序退出")
        return
    
    # 创建可编辑的SVG布局
    svg_content = create_editable_svg_layout(valid_data, colors)
    
    # 保存SVG文件
    output_dir = "03_图表输出/论文级图表"
    svg_file = save_editable_svg(svg_content, output_dir)
    
    if svg_file:
        # 创建使用指南
        create_svg_usage_guide(svg_file)
        
        print("\n=== 可编辑SVG分子骨架图生成完成 ===")
        print(f"生成的文件: {svg_file}")
        print("图表特点:")
        print("- 真正的矢量SVG格式")
        print("- 每个分子都是独立的可编辑元素")
        print("- 支持Adobe Illustrator、Inkscape等编辑")
        print("- 可修改颜色、字体、布局等所有属性")
        print("- 适合专利申请的定制化需求")
        print("- 已排除Scaffold_22")
        
        # 显示选中的分子骨架列表
        print("\n选中的分子骨架:")
        for i, data in enumerate(valid_data, 1):
            print(f"{i:2d}. {data['id']}: {data['description']}")
        
        print(f"\n💡 使用提示:")
        print("1. 用Adobe Illustrator或Inkscape打开SVG文件")
        print("2. 每个分子是一个组，可以取消组合后编辑")
        print("3. 可修改颜色、移动位置、调整大小")
        print("4. 文本标签可独立编辑")
        print("5. 查看使用指南了解更多编辑技巧")

if __name__ == "__main__":
    main()
