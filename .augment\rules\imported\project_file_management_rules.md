---
type: "agent_requested"
---

 # **通用项目文件管理与实验记录规则 v2.0**

## **核心原则**

所有新生成的文件必须遵循灵活的实验分类、智能命名规范和记录要求，确保项目的可追溯性和科学严谨性。规则将根据项目上下文自动适配。

---

## **一、智能文件夹分类与结构规范**

### **1.1 主实验目录结构模板**
```
experiments/
├── {context_category_1}/           # 根据项目上下文自动确定的分类1
├── {context_category_2}/           # 根据项目上下文自动确定的分类2  
├── {context_category_3}/           # 根据项目上下文自动确定的分类3
├── {context_category_4}/           # 根据项目上下文自动确定的分类4
├── {context_category_5}/           # 根据项目上下文自动确定的分类5
├── comparative_studies/            # 对比研究实验
├── validation/                     # 验证实验
├── exploratory/                    # 探索性实验
├── visualization/                  # 数据可视化实验
├── statistical_analysis/          # 统计分析实验
└── archive/                        # 归档实验
```

### **1.2 上下文自适应分类策略**
AI将根据以下线索自动确定实验分类：
- **项目文件内容分析**: 扫描代码、数据文件、配置文件
- **目录结构识别**: 分析现有目录结构和命名模式
- **用户历史**: 参考记忆库中的项目背景和目标
- **领域特征**: 识别学科领域（化学、生物、机器学习、统计等）

### **1.3 单个实验目录结构**
```
{experiment_category}/{YYYY-MM-DD}_{experiment_name}/
├── exp-memory-bank/                # 实验专属记忆库
│   ├── experiment_design.md        # 实验设计文档
│   ├── hypothesis.md              # 研究假设
│   ├── methodology.md             # 实验方法
│   ├── parameters.md              # 参数配置
│   ├── results_summary.md         # 结果总结
│   └── conclusions.md             # 结论分析
├── data/                          # 实验数据
│   ├── input/                     # 输入数据
│   ├── processed/                 # 处理后数据
│   └── output/                    # 输出结果
├── scripts/                       # 实验脚本
├── notebooks/                     # Jupyter笔记本
├── logs/                          # 日志文件
├── plots/                         # 图表输出
├── configs/                       # 配置文件
└── README.md                      # 实验说明
```

---

## **二、智能文件命名规范**

### **2.1 实验文件夹命名**
**格式**: `{YYYY-MM-DD}_{experiment_type}_{context_specific_description}`

**自动生成策略**:
- `{experiment_type}`: 根据脚本内容和用户意图自动识别
- `{context_specific_description}`: 基于项目领域和具体任务智能生成

**示例模式**:
```python
# 机器学习项目示例
"2024-07-16_baseline_{model_name}_{target_variable}"
"2024-07-16_hyperopt_{algorithm}_{optimization_metric}"

# 化学信息学项目示例  
"2024-07-16_docking_{ligand_type}_{target_protein}"
"2024-07-16_qsar_{descriptor_type}_{endpoint}"

# 数据分析项目示例
"2024-07-16_eda_{dataset_name}_{analysis_focus}"
"2024-07-16_stats_{test_type}_{variable_group}"
```

### **2.2 脚本文件命名**
**格式**: `{序号}_{功能描述}_{context_suffix}_{版本}.py`

**自动识别功能类型**:
- 数据处理: `data_loader`, `preprocessor`, `cleaner`
- 特征工程: `feature_extractor`, `transformer`, `selector`
- 模型相关: `trainer`, `predictor`, `evaluator`
- 分析工具: `analyzer`, `visualizer`, `reporter`

### **2.3 数据文件命名**
**格式**: `{data_type}_{processing_stage}_{context_info}_{timestamp}.{ext}`

**上下文信息自动提取**:
- 数据集名称
- 处理方法
- 变量类型
- 实验条件

### **2.4 结果文件命名**
**格式**: `{result_type}_{experiment_context}_{timestamp}.{ext}`

---

## **三、上下文感知时间管理**

### **3.1 智能时间戳策略**
```python
def generate_timestamp(context_type):
    """根据上下文类型生成适当的时间戳"""
    base_format = "%Y-%m-%d"
    
    if context_type in ["quick_test", "debug"]:
        return f"{base_format}_%H-%M-%S"
    elif context_type in ["major_experiment", "publication"]:
        return f"{base_format}_v{semantic_version}"
    else:
        return base_format
```

### **3.2 实验周期智能记录**
系统将根据实验类型自动设置合适的时间记录粒度：
- **快速测试**: 分钟级精度
- **标准实验**: 小时级精度  
- **长期研究**: 天级精度

---

## **四、动态实验记录模板**

### **4.1 实验设计文档模板生成器**
```markdown
# {项目类型}实验设计文档

## 基本信息
- **实验编号**: EXP-{YYYY-MM-DD}-{自动编号}
- **实验标题**: {基于上下文自动生成的描述性标题}
- **研究者**: {从配置或git信息提取}
- **开始日期**: {当前日期}
- **预计完成日期**: {基于实验类型智能估算}

## 项目背景
{根据记忆库自动填充项目背景}

## 研究目标
{基于用户指令和上下文生成具体目标}

## 实验假设
{如果是假设驱动研究，自动生成假设模板}

## 技术路线
{根据项目类型和工具栈生成技术路线}

## 成功标准
{基于项目目标自动生成量化指标}

## 风险评估
{根据实验类型识别常见风险}
```

### **4.2 上下文相关参数记录**
系统将根据检测到的工具和方法自动生成相关参数记录模板：

```python
# 机器学习上下文
if detected_context == "machine_learning":
    return {
        "model_parameters": "模型架构和超参数",
        "training_parameters": "训练配置参数", 
        "data_parameters": "数据集和预处理参数",
        "evaluation_parameters": "评估指标和验证策略"
    }

# 化学信息学上下文  
elif detected_context == "cheminformatics":
    return {
        "molecular_parameters": "分子描述符和特征",
        "chemical_parameters": "化学条件和反应参数",
        "computational_parameters": "计算方法和软件设置",
        "validation_parameters": "验证数据集和评估标准"
    }
```

---

## **五、智能脚本头部生成器**

### **5.1 上下文感知头部模板**
```python
def generate_script_header(context_info):
    """根据上下文生成适当的脚本头部"""
    
    base_header = f"""
# ============================================================================
# {context_info['project_type']}实验脚本
# ============================================================================
# 作者: {context_info['author']}
# 邮箱: {context_info['email']}
# 创建日期: {context_info['date']}
# 最后修改: {context_info['date']}
# 实验编号: EXP-{context_info['date']}-{context_info['exp_id']}
# 实验类型: {context_info['experiment_type']}
# 依赖项目: {context_info['project_name']}
# ============================================================================
# 实验描述: {context_info['description']}
# 输入数据: {context_info['input_data']}
# 输出结果: {context_info['output_data']}
# 核心方法: {context_info['methodology']}
# ============================================================================
"""
    
    # 根据上下文添加特定导入
    context_imports = get_context_imports(context_info['project_type'])
    
    return base_header + context_imports
```

### **5.2 领域特定导入生成**
```python
def get_context_imports(project_type):
    """根据项目类型生成相关导入语句"""
    
    common_imports = """
import sys
import os
from datetime import datetime
import logging
import pandas as pd
import numpy as np
"""
    
    if "machine_learning" in project_type.lower():
        return common_imports + """
import sklearn
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
"""
    
    elif "cheminformatics" in project_type.lower():
        return common_imports + """
from rdkit import Chem
from rdkit.Chem import Descriptors
import mordred
"""
    
    elif "visualization" in project_type.lower():
        return common_imports + """
import matplotlib.pyplot as plt
import seaborn as sns
"""
    
    return common_imports
```

---

## **六、上下文自适应质量控制**

### **6.1 领域特定检查规则**
```python
def get_quality_checks(context):
    """根据上下文返回适当的质量检查清单"""
    
    base_checks = [
        "实验记忆库完整性检查",
        "文件命名规范符合性检查",
        "时间戳格式正确性检查",
        "依赖环境可用性检查"
    ]
    
    if context == "machine_learning":
        return base_checks + [
            "随机种子设置检查",
            "数据泄露防护检查", 
            "模型评估指标完整性检查"
        ]
    
    elif context == "cheminformatics":
        return base_checks + [
            "SMILES字符串有效性检查",
            "分子描述符计算完整性检查",
            "化学数据库版本记录检查"
        ]
    
    return base_checks
```

### **6.2 智能实验类型识别**
```python
def detect_experiment_type(file_content, directory_structure):
    """基于文件内容和目录结构智能识别实验类型"""
    
    keywords_mapping = {
        "machine_learning": ["sklearn", "tensorflow", "pytorch", "model", "train"],
        "cheminformatics": ["rdkit", "smiles", "molecular", "chemical", "drug"],
        "statistics": ["scipy.stats", "statsmodels", "hypothesis", "p_value"],
        "visualization": ["matplotlib", "seaborn", "plotly", "graph", "chart"],
        "data_processing": ["pandas", "numpy", "clean", "preprocess", "transform"]
    }
    
    scores = {}
    for category, keywords in keywords_mapping.items():
        score = sum(1 for keyword in keywords if keyword in file_content.lower())
        scores[category] = score
    
    return max(scores, key=scores.get) if scores else "general"
```

---

## **七、实施自动化流程**

### **7.1 智能实验创建工作流**
```python
def create_experiment(user_input, project_context):
    """智能创建实验的完整工作流"""
    
    # 1. 分析用户意图和项目上下文
    experiment_type = detect_experiment_type(user_input, project_context)
    
    # 2. 生成上下文相关的实验名称
    experiment_name = generate_experiment_name(experiment_type, user_input)
    
    # 3. 创建目录结构
    create_experiment_directory(experiment_type, experiment_name)
    
    # 4. 生成实验记忆库
    generate_experiment_memory_bank(experiment_type, user_input)
    
    # 5. 创建上下文相关的脚本模板
    generate_script_templates(experiment_type)
    
    # 6. 运行质量检查
    run_quality_checks(experiment_type)
    
    # 7. 更新主项目记忆库
    update_main_memory_bank(experiment_name, experiment_type)
```

### **7.2 记忆库智能更新**
系统将根据实验进展自动更新记忆库内容，包括：
- 实验状态变化
- 重要发现记录
- 参数优化历史
- 结果分析总结

---

## **八、使用示例**

当用户提出如下请求时，系统将自动应用这些规则：

**用户**: "我想对这个分子数据集做机器学习预测"
**系统自动执行**:
1. 识别为机器学习+化学信息学上下文
2. 创建 `experiments/cheminformatics_ml/2024-07-16_prediction_molecular_activity/`
3. 生成包含RDKit和sklearn导入的脚本模板
4. 设置分子描述符和ML模型的参数记录模板
5. 应用化学信息学特定的质量检查

**用户**: "分析一下这组实验数据的统计显著性"  
**系统自动执行**:
1. 识别为统计分析上下文
2. 创建 `experiments/statistical_analysis/2024-07-16_significance_test_experimental_data/`
3. 生成包含scipy.stats导入的脚本模板
4. 设置假设检验和效应量的记录模板
5. 应用统计分析特定的质量检查

此通用规则确保了在不同项目上下文中都能提供合适的文件管理和实验记录方案，同时保持高度的灵活性和智能化。