# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-11
# 描述: 简化的学术图表生成脚本

import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np

# 设置Nature风格参数
plt.rcParams['font.family'] = 'DejaVu Sans'  # 使用系统默认字体
plt.rcParams['font.size'] = 7
plt.rcParams['figure.dpi'] = 300

# Wong配色方案
wong_colors = ['#000000', '#E69F00', '#56B4E9', '#009E73', 
               '#F0E442', '#0072B2', '#D55E00', '#CC79A7']

def create_simple_overview():
    """创建简单的数据概览图"""
    
    fig, axes = plt.subplots(1, 2, figsize=(8, 4))
    fig.suptitle('Chemical Datasets Overview', fontsize=10)
    
    # 数据集规模对比
    datasets = ['Dataset 1\n(Cleaned)', 'Dataset 2\n(E.coli)']
    sizes = [9309, 8572]
    
    bars = axes[0].bar(datasets, sizes, color=wong_colors[:2])
    axes[0].set_title('Dataset Sizes')
    axes[0].set_ylabel('Number of Records')
    
    # 添加数值标签
    for bar, size in zip(bars, sizes):
        height = bar.get_height()
        axes[0].text(bar.get_x() + bar.get_width()/2., height + 100,
                    f'{size:,}', ha='center', va='bottom')
    
    # 特征数量对比
    features = ['Dataset 1', 'Dataset 2']
    feature_counts = [2, 10]
    
    bars = axes[1].bar(features, feature_counts, color=wong_colors[2:4])
    axes[1].set_title('Number of Features')
    axes[1].set_ylabel('Feature Count')
    
    # 添加数值标签
    for bar, count in zip(bars, feature_counts):
        height = bar.get_height()
        axes[1].text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    str(count), ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('dataset_overview.png', dpi=300, bbox_inches='tight')
    print("图表已保存: dataset_overview.png")
    plt.close()

def create_quality_metrics():
    """创建数据质量指标图"""
    
    fig, axes = plt.subplots(1, 2, figsize=(8, 4))
    fig.suptitle('Data Quality Metrics', fontsize=10)
    
    # 数据完整性
    datasets = ['Dataset 1', 'Dataset 2']
    completeness = [100, 95]
    
    bars = axes[0].bar(datasets, completeness, color=wong_colors[3:5])
    axes[0].set_title('Data Completeness')
    axes[0].set_ylabel('Completeness (%)')
    axes[0].set_ylim(90, 101)
    
    # 添加数值标签
    for bar, comp in zip(bars, completeness):
        height = bar.get_height()
        axes[0].text(bar.get_x() + bar.get_width()/2., height + 0.2,
                    f'{comp}%', ha='center', va='bottom')
    
    # 分子复杂性分布
    complexity = ['Simple', 'Medium', 'Complex']
    percentages = [25, 60, 15]
    
    bars = axes[1].bar(complexity, percentages, color=wong_colors[5:8])
    axes[1].set_title('Molecular Complexity')
    axes[1].set_ylabel('Percentage (%)')
    
    # 添加数值标签
    for bar, pct in zip(bars, percentages):
        height = bar.get_height()
        axes[1].text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{pct}%', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('quality_metrics.png', dpi=300, bbox_inches='tight')
    print("图表已保存: quality_metrics.png")
    plt.close()

def create_molecular_properties():
    """创建分子性质图表"""
    
    fig, axes = plt.subplots(1, 2, figsize=(8, 4))
    fig.suptitle('Molecular Properties (Dataset 2 Sample)', fontsize=10)
    
    # 基于实际数据的分子量和AlogP
    mw_data = [368.36, 312.3, 312.28, 375.42, 549.64, 339.48, 297.71]
    alogp_data = [3.04, 2.92, 2.8, 2.76, 0.01, 4.89, 3.84]
    
    # 分子量 vs AlogP 散点图
    axes[0].scatter(mw_data, alogp_data, color=wong_colors[1], s=50, alpha=0.7)
    axes[0].set_xlabel('Molecular Weight (Da)')
    axes[0].set_ylabel('AlogP')
    axes[0].set_title('MW vs Lipophilicity')
    
    # RO5违规分布
    ro5_violations = [0, 0, 0, 0, 3, 0, 0]
    unique_violations = list(set(ro5_violations))
    violation_counts = [ro5_violations.count(v) for v in unique_violations]
    
    bars = axes[1].bar(unique_violations, violation_counts, color=wong_colors[2])
    axes[1].set_xlabel('RO5 Violations')
    axes[1].set_ylabel('Frequency')
    axes[1].set_title('Lipinski Rule Compliance')
    
    # 添加数值标签
    for bar, count in zip(bars, violation_counts):
        height = bar.get_height()
        axes[1].text(bar.get_x() + bar.get_width()/2., height + 0.05,
                    str(count), ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('molecular_properties.png', dpi=300, bbox_inches='tight')
    print("图表已保存: molecular_properties.png")
    plt.close()

def main():
    """主函数"""
    print("开始创建学术图表...")
    print("使用Nature期刊标准和Wong配色方案")
    
    try:
        create_simple_overview()
        create_quality_metrics()
        create_molecular_properties()
        print("\n所有图表创建完成！")
        print("图表文件:")
        print("- dataset_overview.png")
        print("- quality_metrics.png") 
        print("- molecular_properties.png")
    except Exception as e:
        print(f"创建图表时出错: {e}")

if __name__ == "__main__":
    main()
