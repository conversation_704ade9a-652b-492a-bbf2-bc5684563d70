# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-14
# 描述: Python环境配置检查和诊断工具

import sys
import subprocess
import importlib

def check_python_version():
    """检查Python版本"""
    print("=== Python环境检查 ===")
    print(f"Python版本: {sys.version}")
    
    version_info = sys.version_info
    if version_info.major == 3 and version_info.minor >= 8:
        print("✓ Python版本符合要求 (>=3.8)")
        return True
    else:
        print("✗ Python版本过低，建议使用Python 3.8+")
        return False

def check_package(package_name, import_name=None, conda_install=None):
    """检查单个包的安装状态"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'Unknown')
        print(f"✓ {package_name}: {version}")
        return True
    except ImportError:
        print(f"✗ {package_name}: 未安装")
        if conda_install:
            print(f"  安装命令: conda install -c conda-forge {conda_install}")
        else:
            print(f"  安装命令: pip install {package_name}")
        return False

def check_rdkit_functionality():
    """检查RDKit功能"""
    print("\n=== RDKit功能测试 ===")
    try:
        from rdkit import Chem
        from rdkit.Chem import Draw
        
        # 测试SMILES解析
        test_smiles = "c1ccccc1"  # 苯环
        mol = Chem.MolFromSmiles(test_smiles)
        if mol is not None:
            print(f"✓ SMILES解析测试通过: {test_smiles}")
        else:
            print("✗ SMILES解析测试失败")
            return False
        
        # 测试分子图像生成
        img = Draw.MolToImage(mol, size=(200, 200))
        if img is not None:
            print("✓ 分子图像生成测试通过")
        else:
            print("✗ 分子图像生成测试失败")
            return False
        
        return True
    except Exception as e:
        print(f"✗ RDKit功能测试失败: {str(e)}")
        return False

def check_pil_functionality():
    """检查PIL功能"""
    print("\n=== PIL功能测试 ===")
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 测试图像创建
        img = Image.new('RGB', (100, 100), color='white')
        draw = ImageDraw.Draw(img)
        draw.text((10, 10), "Test", fill='black')
        print("✓ PIL图像创建和文本绘制测试通过")
        
        # 测试字体
        try:
            font = ImageFont.load_default()
            print("✓ 默认字体加载成功")
        except:
            print("⚠ 默认字体加载失败，但不影响基本功能")
        
        return True
    except Exception as e:
        print(f"✗ PIL功能测试失败: {str(e)}")
        return False

def check_numpy_compatibility():
    """检查numpy兼容性"""
    print("\n=== Numpy兼容性检查 ===")
    try:
        import numpy as np
        print(f"✓ Numpy版本: {np.__version__}")
        
        # 检查是否是numpy 2.x
        if np.__version__.startswith('2.'):
            print("⚠ 检测到Numpy 2.x，可能存在兼容性问题")
            print("  如果遇到问题，建议降级: pip install 'numpy<2.0.0'")
        else:
            print("✓ Numpy版本兼容性良好")
        
        return True
    except ImportError:
        print("✓ Numpy未安装（RDKit和PIL不强制依赖）")
        return True

def provide_installation_guide():
    """提供安装指南"""
    print("\n=== 安装指南 ===")
    print("推荐安装方法（选择其一）：")
    print()
    print("方法1: 使用conda（推荐）")
    print("conda create -n molecular python=3.9")
    print("conda activate molecular")
    print("conda install -c conda-forge rdkit")
    print("pip install Pillow")
    print()
    print("方法2: 使用pip")
    print("python -m venv venv_molecular")
    print("venv_molecular\\Scripts\\activate  # Windows")
    print("# source venv_molecular/bin/activate  # Linux/Mac")
    print("pip install rdkit Pillow")
    print()
    print("方法3: 如果遇到numpy兼容性问题")
    print("pip install 'numpy<2.0.0' rdkit Pillow")

def main():
    """主检查函数"""
    print("分子骨架真实结构图生成器 - 环境配置检查")
    print("=" * 50)
    
    # 检查Python版本
    python_ok = check_python_version()
    
    # 检查必要的包
    print("\n=== 必要包检查 ===")
    rdkit_ok = check_package("rdkit", "rdkit", "rdkit")
    pil_ok = check_package("Pillow", "PIL")
    
    # 检查可选包
    print("\n=== 可选包检查 ===")
    check_package("numpy", "numpy")
    check_package("matplotlib", "matplotlib")
    
    # 功能测试
    if rdkit_ok:
        rdkit_func_ok = check_rdkit_functionality()
    else:
        rdkit_func_ok = False
    
    if pil_ok:
        pil_func_ok = check_pil_functionality()
    else:
        pil_func_ok = False
    
    # 兼容性检查
    check_numpy_compatibility()
    
    # 总结
    print("\n=== 环境检查总结 ===")
    if python_ok and rdkit_ok and pil_ok and rdkit_func_ok and pil_func_ok:
        print("✓ 环境配置完整，可以运行分子骨架真实结构图生成器")
        print("运行命令: python 分子骨架真实结构图生成器.py")
    else:
        print("✗ 环境配置不完整，请按照以下指南安装缺失的包")
        provide_installation_guide()

if __name__ == "__main__":
    main()
