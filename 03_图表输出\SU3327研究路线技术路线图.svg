<?xml version="1.0" encoding="UTF-8"?>
<svg width="210mm" height="297mm" viewBox="0 0 794 1123" xmlns="http://www.w3.org/2000/svg">
  <!-- 样式定义 -->
  <defs>
    <!-- 蓝色渐变定义 -->
    <linearGradient id="blueGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="blueGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#bbdefb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90caf9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="blueGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#64b5f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#42a5f5;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="15" markerHeight="12" refX="13" refY="6" orient="auto">
      <polygon points="0 0, 15 6, 0 12" fill="#1976d2" />
    </marker>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
    
    <style>
      /* 字体样式 */
      .main-title { 
        font-family: "Microsoft YaHei", "SimHei", Arial, sans-serif; 
        font-size: 28px; 
        font-weight: bold; 
        fill: #0d47a1; 
        text-anchor: middle;
      }
      
      .stage-title { 
        font-family: "Microsoft YaHei", "SimHei", Arial, sans-serif; 
        font-size: 20px; 
        font-weight: bold; 
        fill: #1565c0; 
        text-anchor: middle;
      }
      
      .module-title { 
        font-family: "Microsoft YaHei", "SimHei", Arial, sans-serif; 
        font-size: 16px; 
        font-weight: bold; 
        fill: #1976d2; 
        text-anchor: middle;
      }
      
      .content-text { 
        font-family: "Microsoft YaHei", "SimHei", Arial, sans-serif; 
        font-size: 13px; 
        fill: #424242; 
        text-anchor: middle;
      }
      
      .method-text { 
        font-family: "Microsoft YaHei", "SimHei", Arial, sans-serif; 
        font-size: 11px; 
        fill: #616161; 
        text-anchor: middle;
      }
      
      /* 形状样式 */
      .main-container { 
        fill: url(#blueGradient1); 
        stroke: #1976d2; 
        stroke-width: 3; 
        filter: url(#shadow);
      }
      
      .stage-container { 
        fill: url(#blueGradient2); 
        stroke: #1565c0; 
        stroke-width: 2.5; 
        filter: url(#shadow);
      }
      
      .module-container { 
        fill: #ffffff; 
        stroke: #2196f3; 
        stroke-width: 2; 
        filter: url(#shadow);
      }
      
      .method-container { 
        fill: #f3f8ff; 
        stroke: #64b5f6; 
        stroke-width: 1.5; 
      }
      
      /* 连接线 */
      .main-arrow { 
        stroke: #1976d2; 
        stroke-width: 6; 
        fill: none; 
        marker-end: url(#arrowhead);
      }
      
      .connection-line { 
        stroke: #90caf9; 
        stroke-width: 2; 
        fill: none;
      }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#ffffff"/>
  
  <!-- 主标题 -->
  <text x="397" y="80" class="main-title">SU3327研究路线</text>
  
  <!-- 第一阶段：活性与安全性评价 -->
  <g id="stage1">
    <!-- 主容器 -->
    <rect x="97" y="140" width="600" height="320" class="main-container" rx="20"/>
    
    <!-- 阶段标题 -->
    <text x="397" y="180" class="stage-title">第一阶段：活性与安全性评价</text>
    
    <!-- 体外活性模块 -->
    <g id="in-vitro">
      <rect x="130" y="220" width="250" height="200" class="module-container" rx="15"/>
      <text x="255" y="250" class="module-title">体外活性</text>
      
      <!-- 体外活性方法 -->
      <rect x="145" y="270" width="220" height="25" class="method-container" rx="8"/>
      <text x="255" y="287" class="method-text">最小抑菌浓度(MIC)测定</text>
      
      <rect x="145" y="305" width="220" height="25" class="method-container" rx="8"/>
      <text x="255" y="322" class="method-text">最小杀菌浓度(MBC)测定</text>
      
      <rect x="145" y="340" width="220" height="25" class="method-container" rx="8"/>
      <text x="255" y="357" class="method-text">时间-杀菌曲线分析</text>
      
      <rect x="145" y="375" width="220" height="25" class="method-container" rx="8"/>
      <text x="255" y="392" class="method-text">耐药性诱导实验</text>
    </g>
    
    <!-- 体内药效模块 -->
    <g id="in-vivo">
      <rect x="414" y="220" width="250" height="200" class="module-container" rx="15"/>
      <text x="539" y="250" class="module-title">体内药效</text>
      
      <!-- 体内药效方法 -->
      <rect x="429" y="270" width="220" height="25" class="method-container" rx="8"/>
      <text x="539" y="287" class="method-text">小鼠感染模型建立</text>
      
      <rect x="429" y="305" width="220" height="25" class="method-container" rx="8"/>
      <text x="539" y="322" class="method-text">药效学参数测定</text>
      
      <rect x="429" y="340" width="220" height="25" class="method-container" rx="8"/>
      <text x="539" y="357" class="method-text">安全性毒理学评价</text>
      
      <rect x="429" y="375" width="220" height="25" class="method-container" rx="8"/>
      <text x="539" y="392" class="method-text">药代动力学研究</text>
    </g>
    
    <!-- 连接线 -->
    <line x1="255" y1="420" x2="255" y2="440" class="connection-line"/>
    <line x1="539" y1="420" x2="539" y2="440" class="connection-line"/>
    <line x1="255" y1="440" x2="539" y2="440" class="connection-line"/>
    <line x1="397" y1="440" x2="397" y2="460" class="connection-line"/>
  </g>
  
  <!-- 主要流程箭头 -->
  <path d="M 397 480 L 397 540" class="main-arrow"/>
  
  <!-- 第二阶段：DNA损伤机制探索 -->
  <g id="stage2">
    <!-- 主容器 -->
    <rect x="97" y="560" width="600" height="480" class="main-container" rx="20"/>
    
    <!-- 阶段标题 -->
    <text x="397" y="600" class="stage-title">第二阶段：DNA损伤机制探索</text>
    
    <!-- 机制验证模块 -->
    <g id="mechanism">
      <rect x="130" y="640" width="250" height="160" class="module-container" rx="15"/>
      <text x="255" y="670" class="module-title">机制验证</text>
      
      <rect x="145" y="690" width="220" height="25" class="method-container" rx="8"/>
      <text x="255" y="707" class="method-text">硝基还原酶抑制剂实验</text>
      
      <rect x="145" y="725" width="220" height="25" class="method-container" rx="8"/>
      <text x="255" y="742" class="method-text">酶活性检测分析</text>
      
      <rect x="145" y="760" width="220" height="25" class="method-container" rx="8"/>
      <text x="255" y="777" class="method-text">分子对接模拟验证</text>
    </g>
    
    <!-- 药物转化模块 -->
    <g id="transformation">
      <rect x="414" y="640" width="250" height="160" class="module-container" rx="15"/>
      <text x="539" y="670" class="module-title">药物转化</text>
      
      <rect x="429" y="690" width="220" height="25" class="method-container" rx="8"/>
      <text x="539" y="707" class="method-text">胞内药物浓度检测</text>
      
      <rect x="429" y="725" width="220" height="25" class="method-container" rx="8"/>
      <text x="539" y="742" class="method-text">HPLC-MS/MS分析</text>
      
      <rect x="429" y="760" width="220" height="25" class="method-container" rx="8"/>
      <text x="539" y="777" class="method-text">药物代谢途径追踪</text>
    </g>
    
    <!-- 产物鉴定模块 -->
    <g id="identification">
      <rect x="130" y="840" width="250" height="160" class="module-container" rx="15"/>
      <text x="255" y="870" class="module-title">产物鉴定</text>
      
      <rect x="145" y="890" width="220" height="25" class="method-container" rx="8"/>
      <text x="255" y="907" class="method-text">LC-MS/MS代谢物分析</text>
      
      <rect x="145" y="925" width="220" height="25" class="method-container" rx="8"/>
      <text x="255" y="942" class="method-text">NMR结构确证</text>
      
      <rect x="145" y="960" width="220" height="25" class="method-container" rx="8"/>
      <text x="255" y="977" class="method-text">代谢产物活性评价</text>
    </g>
    
    <!-- 后果求证模块 -->
    <g id="consequence">
      <rect x="414" y="840" width="250" height="160" class="module-container" rx="15"/>
      <text x="539" y="870" class="module-title">后果求证</text>
      
      <rect x="429" y="890" width="220" height="25" class="method-container" rx="8"/>
      <text x="539" y="907" class="method-text">DNA损伤标志物检测</text>
      
      <rect x="429" y="925" width="220" height="25" class="method-container" rx="8"/>
      <text x="539" y="942" class="method-text">自由基生成检测</text>
      
      <rect x="429" y="960" width="220" height="25" class="method-container" rx="8"/>
      <text x="539" y="977" class="method-text">DNA完整性评估</text>
    </g>
    
    <!-- 第二阶段内部连接线 -->
    <line x1="255" y1="800" x2="255" y2="840" class="connection-line"/>
    <line x1="539" y1="800" x2="539" y2="840" class="connection-line"/>
    <line x1="380" y1="720" x2="414" y2="720" class="connection-line"/>
    <line x1="380" y1="920" x2="414" y2="920" class="connection-line"/>
  </g>
  
  <!-- 底部总结框 -->
  <rect x="147" y="1070" width="500" height="40" class="stage-container" rx="15"/>
  <text x="397" y="1095" class="stage-title">SU3327抗菌机制完整阐释</text>
  
  <!-- 最终连接箭头 -->
  <path d="M 397 1020 L 397 1050" class="main-arrow"/>
  
</svg>
