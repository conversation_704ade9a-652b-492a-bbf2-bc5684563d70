# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-14
# 描述: 使用RDKit生成分子骨架结构图 - 简化版本（不依赖pandas）

import csv
import os
import sys

# 尝试导入必要的包
try:
    from rdkit import Chem
    from rdkit.Chem import Draw
    print("✓ RDKit导入成功")
except ImportError:
    print("✗ RDKit未安装，请先安装: pip install rdkit")
    sys.exit(1)

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.gridspec import GridSpec
    print("✓ Matplotlib导入成功")
except ImportError:
    print("✗ Matplotlib未安装，请先安装: pip install matplotlib")
    sys.exit(1)

def setup_chinese_style():
    """设置中国风学术图表样式"""
    colors = {
        'primary': '#C0392B',      # 朱红
        'secondary': '#2980B9',    # 靛蓝  
        'accent1': '#27AE60',      # 石绿
        'accent2': '#F1C40F',      # 藤黄
        'accent3': '#A0522D',      # 赭石
        'text': '#34495E',         # 青金
        'background': '#FDFEFE'    # 定窑白
    }
    
    # 设置matplotlib参数
    plt.rcParams.update({
        'font.size': 10,
        'font.family': 'Arial',
        'axes.titlesize': 12,
        'axes.labelsize': 10,
        'figure.titlesize': 14,
        'axes.linewidth': 0.8,
        'axes.edgecolor': colors['text'],
        'text.color': colors['text'],
        'axes.labelcolor': colors['text']
    })
    
    return colors

def load_scaffold_data():
    """加载分子骨架数据"""
    print("=== 分子骨架结构图生成器（简化版）===")
    print("加载分子骨架数据...")
    
    data_path = "01_原始数据/分子骨架数据/scaffold_smiles_data.csv"
    
    scaffolds = []
    try:
        with open(data_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                scaffolds.append({
                    'id': row['Scaffold_ID'],
                    'smiles': row['SMILES'],
                    'description': row['Description']
                })
    except FileNotFoundError:
        print(f"✗ 数据文件未找到: {data_path}")
        return []
    except Exception as e:
        print(f"✗ 读取数据文件失败: {str(e)}")
        return []
    
    print(f"数据加载完成，共 {len(scaffolds)} 个分子骨架")
    return scaffolds

def validate_smiles(scaffolds):
    """验证SMILES格式并创建分子对象"""
    print("验证SMILES格式...")
    
    valid_data = []
    
    for scaffold in scaffolds:
        try:
            mol = Chem.MolFromSmiles(scaffold['smiles'])
            if mol is not None:
                valid_data.append({
                    'mol': mol,
                    'id': scaffold['id'],
                    'smiles': scaffold['smiles'],
                    'description': scaffold['description']
                })
                print(f"  ✓ {scaffold['id']}: 验证成功")
            else:
                print(f"  ✗ {scaffold['id']}: SMILES格式无效")
        except Exception as e:
            print(f"  ✗ {scaffold['id']}: 处理失败 - {str(e)}")
    
    print(f"成功验证 {len(valid_data)} 个分子结构")
    return valid_data

def create_scaffold_grid_figure(valid_data, colors):
    """创建分子骨架网格图表"""
    print("创建分子骨架网格图表...")
    
    n_molecules = len(valid_data)
    n_cols = 4
    n_rows = (n_molecules + n_cols - 1) // n_cols  # 向上取整
    
    # 创建图形
    fig = plt.figure(figsize=(16, 20), facecolor=colors['background'])
    
    # 设置网格布局
    gs = GridSpec(n_rows, n_cols, 
                  figure=fig,
                  hspace=0.3, 
                  wspace=0.2,
                  top=0.95,
                  bottom=0.05,
                  left=0.05,
                  right=0.95)
    
    # 为每个分子创建子图
    for i, data in enumerate(valid_data):
        row = i // n_cols
        col = i % n_cols
        
        ax = fig.add_subplot(gs[row, col])
        
        try:
            # 生成分子图像
            img = Draw.MolToImage(data['mol'], size=(400, 400), kekulize=True)
            
            # 显示分子结构
            ax.imshow(img)
            ax.axis('off')
            
            # 添加分子ID标题
            ax.set_title(data['id'], 
                        fontsize=12, 
                        fontweight='bold',
                        color=colors['text'],
                        pad=10)
            
            # 添加SMILES信息（简化显示）
            smiles_short = data['smiles'][:30] + "..." if len(data['smiles']) > 30 else data['smiles']
            ax.text(0.5, -0.1, smiles_short,
                   transform=ax.transAxes,
                   ha='center',
                   va='top',
                   fontsize=8,
                   color=colors['text'],
                   style='italic')
            
        except Exception as e:
            # 如果绘制失败，显示错误信息
            ax.text(0.5, 0.5, f"Error\n{data['id']}",
                   transform=ax.transAxes,
                   ha='center',
                   va='center',
                   fontsize=12,
                   color='red',
                   weight='bold')
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
    
    # 隐藏多余的子图
    for i in range(n_molecules, n_rows * n_cols):
        row = i // n_cols
        col = i % n_cols
        ax = fig.add_subplot(gs[row, col])
        ax.axis('off')
    
    # 添加总标题
    fig.suptitle('Representative Molecular Scaffolds - Chemical Structure Analysis',
                fontsize=18,
                fontweight='bold',
                color=colors['text'],
                y=0.98)
    
    # 添加副标题
    fig.text(0.5, 0.02,
            '20 Key Scaffolds from Chemical Database | Generated with RDKit | ZK Research Group',
            ha='center',
            va='bottom',
            fontsize=12,
            color=colors['text'],
            style='italic')
    
    return fig

def save_high_quality_figures(fig, output_dir):
    """保存高质量图表文件"""
    print("保存高质量图表文件...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    files_saved = []
    
    # 保存PNG格式（高分辨率）
    try:
        png_file = os.path.join(output_dir, "分子骨架真实结构图_RDKit_Nature风格.png")
        fig.savefig(png_file, 
                    dpi=300, 
                    bbox_inches='tight',
                    facecolor='white',
                    edgecolor='none',
                    format='png')
        print(f"PNG图片保存完成: {png_file}")
        files_saved.append(png_file)
    except Exception as e:
        print(f"PNG保存失败: {str(e)}")
    
    # 保存PDF格式（矢量图）
    try:
        pdf_file = os.path.join(output_dir, "分子骨架真实结构图_RDKit_Nature风格.pdf")
        fig.savefig(pdf_file,
                    bbox_inches='tight',
                    facecolor='white',
                    edgecolor='none',
                    format='pdf')
        print(f"PDF矢量图保存完成: {pdf_file}")
        files_saved.append(pdf_file)
    except Exception as e:
        print(f"PDF保存失败: {str(e)}")
    
    # 保存SVG格式（矢量图）
    try:
        svg_file = os.path.join(output_dir, "分子骨架真实结构图_RDKit_Nature风格.svg")
        fig.savefig(svg_file,
                    bbox_inches='tight',
                    facecolor='white',
                    edgecolor='none',
                    format='svg')
        print(f"SVG矢量图保存完成: {svg_file}")
        files_saved.append(svg_file)
    except Exception as e:
        print(f"SVG保存失败: {str(e)}")
    
    return files_saved

def main():
    """主函数"""
    # 设置样式
    colors = setup_chinese_style()
    
    # 加载数据
    scaffolds = load_scaffold_data()
    if not scaffolds:
        print("⚠ 无法加载数据，程序退出")
        return
    
    # 验证SMILES
    valid_data = validate_smiles(scaffolds)
    
    if len(valid_data) == 0:
        print("⚠ 没有有效的分子结构，程序退出")
        return
    
    # 创建图表
    fig = create_scaffold_grid_figure(valid_data, colors)
    
    # 保存图表
    output_dir = "03_图表输出/论文级图表"
    files_saved = save_high_quality_figures(fig, output_dir)
    
    print("\n=== 分子骨架结构图生成完成 ===")
    print("生成的文件:")
    for file_path in files_saved:
        print(f"- {file_path}")
    print("图表特点:")
    print("- 真实的2D分子结构图")
    print("- 4x5网格布局")
    print("- Nature期刊风格设计")
    print("- 中国风配色方案")
    print("- 高分辨率输出(300 DPI)")
    print("- 多种格式支持(PNG/PDF/SVG)")

if __name__ == "__main__":
    main()
