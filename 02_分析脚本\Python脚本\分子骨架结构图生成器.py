# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-14
# 描述: 使用RDKit生成分子骨架结构图 - 20个代表性分子骨架

import pandas as pd
import numpy as np
from rdkit import Chem
from rdkit.Chem import Draw
from rdkit.Chem.Draw import rdMolDraw2D
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.gridspec import GridSpec
import os
import warnings
warnings.filterwarnings('ignore')

def setup_chinese_style():
    """设置中国风学术图表样式"""
    # 中国风配色方案
    colors = {
        'primary': '#C0392B',      # 朱红
        'secondary': '#2980B9',    # 靛蓝  
        'accent1': '#27AE60',      # 石绿
        'accent2': '#F1C40F',      # 藤黄
        'accent3': '#A0522D',      # 赭石
        'text': '#34495E',         # 青金
        'background': '#FDFEFE'    # 定窑白
    }
    
    # 设置matplotlib参数
    plt.rcParams.update({
        'font.size': 10,
        'font.family': 'Arial',
        'axes.titlesize': 12,
        'axes.labelsize': 10,
        'xtick.labelsize': 8,
        'ytick.labelsize': 8,
        'legend.fontsize': 9,
        'figure.titlesize': 14,
        'axes.linewidth': 0.8,
        'grid.linewidth': 0.5,
        'lines.linewidth': 1.5,
        'patch.linewidth': 0.5,
        'xtick.major.width': 0.8,
        'ytick.major.width': 0.8,
        'xtick.minor.width': 0.6,
        'ytick.minor.width': 0.6,
        'axes.edgecolor': colors['text'],
        'text.color': colors['text'],
        'axes.labelcolor': colors['text'],
        'xtick.color': colors['text'],
        'ytick.color': colors['text']
    })
    
    return colors

def load_scaffold_data():
    """加载分子骨架数据"""
    print("=== 分子骨架结构图生成器 ===")
    print("加载分子骨架数据...")
    
    data_path = "01_原始数据/分子骨架数据/scaffold_smiles_data.csv"
    df = pd.read_csv(data_path)
    
    print(f"数据加载完成，共 {len(df)} 个分子骨架")
    return df

def validate_smiles(smiles_list, ids):
    """验证SMILES格式并创建分子对象"""
    print("验证SMILES格式...")
    
    valid_molecules = []
    valid_ids = []
    valid_smiles = []
    
    for i, smiles in enumerate(smiles_list):
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is not None:
                valid_molecules.append(mol)
                valid_ids.append(ids[i])
                valid_smiles.append(smiles)
                print(f"  ✓ {ids[i]}: 验证成功")
            else:
                print(f"  ✗ {ids[i]}: SMILES格式无效")
        except Exception as e:
            print(f"  ✗ {ids[i]}: 处理失败 - {str(e)}")
    
    print(f"成功验证 {len(valid_molecules)} 个分子结构")
    return valid_molecules, valid_ids, valid_smiles

def draw_molecule_structure(mol, mol_id, size=(300, 300)):
    """绘制单个分子结构"""
    try:
        # 创建分子绘制器
        drawer = rdMolDraw2D.MolDraw2DCairo(size[0], size[1])
        
        # 设置绘制选项
        opts = drawer.drawOptions()
        opts.addStereoAnnotation = True
        opts.addAtomIndices = False
        opts.bondLineWidth = 2
        
        # 绘制分子
        drawer.DrawMolecule(mol)
        drawer.FinishDrawing()
        
        # 获取图像数据
        img_data = drawer.GetDrawingText()
        
        return img_data
    except Exception as e:
        print(f"绘制分子 {mol_id} 失败: {str(e)}")
        return None

def create_scaffold_grid_figure(molecules, mol_ids, smiles_list, colors):
    """创建分子骨架网格图表"""
    print("创建分子骨架网格图表...")
    
    n_molecules = len(molecules)
    n_cols = 4
    n_rows = int(np.ceil(n_molecules / n_cols))
    
    # 创建图形
    fig = plt.figure(figsize=(16, 20), facecolor=colors['background'])
    
    # 设置网格布局
    gs = GridSpec(n_rows, n_cols, 
                  figure=fig,
                  hspace=0.3, 
                  wspace=0.2,
                  top=0.95,
                  bottom=0.05,
                  left=0.05,
                  right=0.95)
    
    # 为每个分子创建子图
    for i, (mol, mol_id, smiles) in enumerate(zip(molecules, mol_ids, smiles_list)):
        row = i // n_cols
        col = i % n_cols
        
        ax = fig.add_subplot(gs[row, col])
        
        try:
            # 生成分子图像
            img = Draw.MolToImage(mol, size=(400, 400), kekulize=True)
            
            # 显示分子结构
            ax.imshow(img)
            ax.axis('off')
            
            # 添加分子ID标题
            ax.set_title(mol_id, 
                        fontsize=12, 
                        fontweight='bold',
                        color=colors['text'],
                        pad=10)
            
            # 添加SMILES信息（简化显示）
            smiles_short = smiles[:30] + "..." if len(smiles) > 30 else smiles
            ax.text(0.5, -0.1, smiles_short,
                   transform=ax.transAxes,
                   ha='center',
                   va='top',
                   fontsize=8,
                   color=colors['text'],
                   style='italic',
                   wrap=True)
            
        except Exception as e:
            # 如果绘制失败，显示错误信息
            ax.text(0.5, 0.5, f"Error\n{mol_id}",
                   transform=ax.transAxes,
                   ha='center',
                   va='center',
                   fontsize=12,
                   color='red',
                   weight='bold')
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
    
    # 隐藏多余的子图
    for i in range(n_molecules, n_rows * n_cols):
        row = i // n_cols
        col = i % n_cols
        ax = fig.add_subplot(gs[row, col])
        ax.axis('off')
    
    # 添加总标题
    fig.suptitle('Representative Molecular Scaffolds - Chemical Structure Analysis',
                fontsize=18,
                fontweight='bold',
                color=colors['text'],
                y=0.98)
    
    # 添加副标题
    fig.text(0.5, 0.02,
            '20 Key Scaffolds from Chemical Database | Generated with RDKit | ZK Research Group',
            ha='center',
            va='bottom',
            fontsize=12,
            color=colors['text'],
            style='italic')
    
    return fig

def save_high_quality_figures(fig, output_dir):
    """保存高质量图表文件"""
    print("保存高质量图表文件...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存PNG格式（高分辨率）
    png_file = os.path.join(output_dir, "分子骨架真实结构图_RDKit_Nature风格.png")
    fig.savefig(png_file, 
                dpi=300, 
                bbox_inches='tight',
                facecolor='white',
                edgecolor='none',
                format='png')
    print(f"PNG图片保存完成: {png_file}")
    
    # 保存PDF格式（矢量图）
    pdf_file = os.path.join(output_dir, "分子骨架真实结构图_RDKit_Nature风格.pdf")
    fig.savefig(pdf_file,
                bbox_inches='tight',
                facecolor='white',
                edgecolor='none',
                format='pdf')
    print(f"PDF矢量图保存完成: {pdf_file}")
    
    # 保存SVG格式（矢量图）
    svg_file = os.path.join(output_dir, "分子骨架真实结构图_RDKit_Nature风格.svg")
    fig.savefig(svg_file,
                bbox_inches='tight',
                facecolor='white',
                edgecolor='none',
                format='svg')
    print(f"SVG矢量图保存完成: {svg_file}")
    
    return png_file, pdf_file, svg_file

def main():
    """主函数"""
    # 设置样式
    colors = setup_chinese_style()
    
    # 加载数据
    df = load_scaffold_data()
    
    # 验证SMILES
    molecules, mol_ids, smiles_list = validate_smiles(df['SMILES'].tolist(), 
                                                     df['Scaffold_ID'].tolist())
    
    if len(molecules) == 0:
        print("⚠ 没有有效的分子结构，程序退出")
        return
    
    # 创建图表
    fig = create_scaffold_grid_figure(molecules, mol_ids, smiles_list, colors)
    
    # 保存图表
    output_dir = "03_图表输出/论文级图表"
    png_file, pdf_file, svg_file = save_high_quality_figures(fig, output_dir)
    
    # 显示图表（可选）
    plt.show()
    
    print("\n=== 分子骨架结构图生成完成 ===")
    print("生成的文件:")
    print(f"- PNG格式: {png_file}")
    print(f"- PDF格式: {pdf_file}")
    print(f"- SVG格式: {svg_file}")
    print("图表特点:")
    print("- 真实的2D分子结构图")
    print("- 4x5网格布局")
    print("- Nature期刊风格设计")
    print("- 中国风配色方案")
    print("- 高分辨率输出(300 DPI)")
    print("- 多种格式支持(PNG/PDF/SVG)")

if __name__ == "__main__":
    main()
