# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-11
# 描述: 化学空间分析和深度学习数据质量评估

import pandas as pd
import numpy as np
import re
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

def analyze_smiles_complexity(smiles_series, dataset_name):
    """分析SMILES复杂性指标"""
    print(f"\n=== {dataset_name} SMILES复杂性分析 ===")
    
    # 基本长度统计
    lengths = smiles_series.str.len()
    print(f"SMILES长度统计:")
    print(f"  最小长度: {lengths.min()}")
    print(f"  最大长度: {lengths.max()}")
    print(f"  平均长度: {lengths.mean():.2f}")
    print(f"  中位数长度: {lengths.median()}")
    print(f"  标准差: {lengths.std():.2f}")
    
    # 化学元素统计
    elements = ['C', 'N', 'O', 'S', 'P', 'F', 'Cl', 'Br', 'I']
    element_counts = {}
    for element in elements:
        if element in ['Cl', 'Br']:
            count = smiles_series.str.count(element).sum()
        else:
            count = smiles_series.str.count(f'[{element}]').sum() + smiles_series.str.count(element).sum()
        element_counts[element] = count
    
    print(f"\n化学元素频率:")
    for element, count in element_counts.items():
        print(f"  {element}: {count}")
    
    # 结构复杂性指标
    ring_indicators = smiles_series.str.count(r'[0-9]').sum()  # 环闭合数字
    aromatic_atoms = smiles_series.str.count(r'[cnos]').sum()  # 芳香原子
    stereochemistry = smiles_series.str.count('@').sum()  # 立体化学
    double_bonds = smiles_series.str.count('=').sum()
    triple_bonds = smiles_series.str.count('#').sum()
    
    print(f"\n结构复杂性指标:")
    print(f"  环闭合指示符: {ring_indicators}")
    print(f"  芳香原子: {aromatic_atoms}")
    print(f"  立体化学中心: {stereochemistry}")
    print(f"  双键: {double_bonds}")
    print(f"  三键: {triple_bonds}")
    
    return {
        'lengths': lengths,
        'element_counts': element_counts,
        'complexity_metrics': {
            'ring_indicators': ring_indicators,
            'aromatic_atoms': aromatic_atoms,
            'stereochemistry': stereochemistry,
            'double_bonds': double_bonds,
            'triple_bonds': triple_bonds
        }
    }

def analyze_chemical_diversity(smiles_series, dataset_name):
    """分析化学多样性"""
    print(f"\n=== {dataset_name} 化学多样性分析 ===")
    
    # 唯一性分析
    total_smiles = len(smiles_series)
    unique_smiles = smiles_series.nunique()
    duplicate_rate = (total_smiles - unique_smiles) / total_smiles * 100
    
    print(f"分子多样性指标:")
    print(f"  总分子数: {total_smiles}")
    print(f"  唯一分子数: {unique_smiles}")
    print(f"  重复率: {duplicate_rate:.2f}%")
    print(f"  多样性比例: {unique_smiles/total_smiles*100:.2f}%")
    
    # 分子骨架多样性（基于简化的结构模式）
    # 移除立体化学和氢原子信息进行骨架分析
    simplified_smiles = smiles_series.str.replace(r'@+', '', regex=True)
    simplified_smiles = simplified_smiles.str.replace(r'\[.*?H.*?\]', '', regex=True)
    scaffold_diversity = simplified_smiles.nunique()
    
    print(f"  骨架多样性: {scaffold_diversity}")
    print(f"  骨架/分子比例: {scaffold_diversity/unique_smiles*100:.2f}%")
    
    return {
        'total_molecules': total_smiles,
        'unique_molecules': unique_smiles,
        'duplicate_rate': duplicate_rate,
        'scaffold_diversity': scaffold_diversity
    }

def analyze_activity_distribution(activity_series, dataset_name):
    """分析活性分布"""
    print(f"\n=== {dataset_name} 活性分布分析 ===")
    
    # 基本分布统计
    activity_counts = activity_series.value_counts().sort_index()
    activity_props = (activity_counts / len(activity_series) * 100).round(2)
    
    print(f"活性分布:")
    for activity, count in activity_counts.items():
        prop = activity_props[activity]
        print(f"  类别 {activity}: {count} ({prop}%)")
    
    # 类别平衡性评估
    if len(activity_counts) == 2:
        minority_class = activity_counts.min()
        majority_class = activity_counts.max()
        imbalance_ratio = majority_class / minority_class
        
        print(f"\n类别平衡性评估:")
        print(f"  少数类样本数: {minority_class}")
        print(f"  多数类样本数: {majority_class}")
        print(f"  不平衡比例: {imbalance_ratio:.2f}:1")
        
        # 深度学习适用性评估
        if imbalance_ratio <= 3:
            balance_status = "良好平衡"
        elif imbalance_ratio <= 10:
            balance_status = "轻度不平衡"
        else:
            balance_status = "严重不平衡"
        
        print(f"  平衡性评估: {balance_status}")
        
        return {
            'counts': activity_counts,
            'proportions': activity_props,
            'imbalance_ratio': imbalance_ratio,
            'balance_status': balance_status
        }
    
    return {
        'counts': activity_counts,
        'proportions': activity_props
    }

def deep_learning_suitability_assessment(df, dataset_name):
    """深度学习适用性评估"""
    print(f"\n=== {dataset_name} 深度学习适用性评估 ===")
    
    total_samples = len(df)
    
    # 数据规模评估
    print(f"数据规模评估:")
    print(f"  总样本数: {total_samples}")
    
    if total_samples >= 10000:
        size_rating = "优秀"
        size_comment = "数据规模充足，适合深度学习"
    elif total_samples >= 5000:
        size_rating = "良好"
        size_comment = "数据规模较好，适合中等复杂度模型"
    elif total_samples >= 1000:
        size_rating = "一般"
        size_comment = "数据规模有限，建议使用数据增强"
    else:
        size_rating = "不足"
        size_comment = "数据规模过小，不适合深度学习"
    
    print(f"  规模评级: {size_rating}")
    print(f"  评估意见: {size_comment}")
    
    # SMILES质量评估
    smiles_series = df['Smiles']
    invalid_smiles = smiles_series.isnull().sum() + (smiles_series == '').sum()
    smiles_quality = (len(smiles_series) - invalid_smiles) / len(smiles_series) * 100
    
    print(f"\nSMILES质量评估:")
    print(f"  有效SMILES比例: {smiles_quality:.2f}%")
    print(f"  无效/缺失SMILES: {invalid_smiles}")
    
    if smiles_quality >= 95:
        quality_rating = "优秀"
    elif smiles_quality >= 90:
        quality_rating = "良好"
    elif smiles_quality >= 80:
        quality_rating = "一般"
    else:
        quality_rating = "较差"
    
    print(f"  质量评级: {quality_rating}")
    
    # 综合适用性评分
    size_score = {'优秀': 4, '良好': 3, '一般': 2, '不足': 1}[size_rating]
    quality_score = {'优秀': 4, '良好': 3, '一般': 2, '较差': 1}[quality_rating]
    
    overall_score = (size_score + quality_score) / 2
    
    if overall_score >= 3.5:
        suitability = "高度适合"
    elif overall_score >= 2.5:
        suitability = "适合"
    elif overall_score >= 1.5:
        suitability = "需要改进"
    else:
        suitability = "不适合"
    
    print(f"\n综合适用性评估:")
    print(f"  适用性评级: {suitability}")
    print(f"  综合评分: {overall_score:.1f}/4.0")
    
    return {
        'total_samples': total_samples,
        'size_rating': size_rating,
        'smiles_quality': smiles_quality,
        'quality_rating': quality_rating,
        'overall_suitability': suitability,
        'overall_score': overall_score
    }

def main():
    """主分析函数"""
    print("化学空间分析和深度学习数据质量评估")
    print("="*60)
    
    # 加载数据集
    try:
        df1 = pd.read_csv('cleaned_data-12-9-no-duplicate.csv')
        df2 = pd.read_csv('filtered_ecoli_9000-nosalt.csv')
        print("数据集加载成功")
    except Exception as e:
        print(f"数据加载失败: {e}")
        return
    
    # 数据集1分析
    print("\n" + "="*60)
    print("数据集1: cleaned_data-12-9-no-duplicate.csv")
    print("="*60)
    
    smiles1_analysis = analyze_smiles_complexity(df1['Smiles'], "数据集1")
    diversity1_analysis = analyze_chemical_diversity(df1['Smiles'], "数据集1")
    activity1_analysis = analyze_activity_distribution(df1['Activity'], "数据集1")
    dl1_assessment = deep_learning_suitability_assessment(df1, "数据集1")
    
    # 数据集2分析
    print("\n" + "="*60)
    print("数据集2: filtered_ecoli_9000-nosalt.csv")
    print("="*60)
    
    smiles2_analysis = analyze_smiles_complexity(df2['Smiles'], "数据集2")
    diversity2_analysis = analyze_chemical_diversity(df2['Smiles'], "数据集2")
    activity2_analysis = analyze_activity_distribution(df2['Activity'], "数据集2")
    dl2_assessment = deep_learning_suitability_assessment(df2, "数据集2")
    
    # 对比分析
    print("\n" + "="*60)
    print("数据集对比分析")
    print("="*60)
    
    print(f"数据规模对比:")
    print(f"  数据集1: {len(df1)} 样本")
    print(f"  数据集2: {len(df2)} 样本")
    
    print(f"\n化学多样性对比:")
    print(f"  数据集1多样性: {diversity1_analysis['unique_molecules']/diversity1_analysis['total_molecules']*100:.2f}%")
    print(f"  数据集2多样性: {diversity2_analysis['unique_molecules']/diversity2_analysis['total_molecules']*100:.2f}%")
    
    print(f"\n深度学习适用性对比:")
    print(f"  数据集1: {dl1_assessment['overall_suitability']} ({dl1_assessment['overall_score']:.1f}/4.0)")
    print(f"  数据集2: {dl2_assessment['overall_suitability']} ({dl2_assessment['overall_score']:.1f}/4.0)")
    
    print("\n分析完成!")

if __name__ == "__main__":
    main()
