# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-11
# 描述: 化学空间重叠度分析 - R语言版本

# 加载必要的包
suppressMessages({
    library(ggplot2)
    library(dplyr)
    library(readr)
    library(gridExtra)
    library(grid)
})

# 设置随机种子
set.seed(42)

# Nature风格主题
theme_nature <- function(base_size = 8) {
    theme_bw(base_size = base_size) +
        theme(
            panel.grid.major = element_blank(),
            panel.grid.minor = element_blank(),
            panel.border = element_rect(colour = "black", fill = NA, linewidth = 0.5),
            plot.title = element_text(hjust = 0.5, size = rel(1.2)),
            axis.text = element_text(color = "black"),
            axis.ticks = element_line(colour = "black", linewidth = 0.5),
            legend.key = element_blank(),
            strip.background = element_rect(fill = "white", colour = "black")
        )
}

# Wong配色方案
wong_colors <- c("#000000", "#E69F00", "#56B4E9", "#009E73",
                 "#F0E442", "#0072B2", "#D55E00", "#CC79A7")

# 数据加载和抽样函数
load_and_sample_data <- function() {
    cat("正在加载数据集...\n")
    
    # 加载训练数据集
    train1 <- read_csv("cleaned_data-12-9-no-duplicate.csv", show_col_types = FALSE)
    train1$Dataset <- "S. aureus"
    train1$Type <- "Training"
    cat("训练数据集1加载成功:", nrow(train1), "个化合物\n")
    
    train2 <- read_csv("filtered_ecoli_9000-nosalt.csv", show_col_types = FALSE)
    train2$Dataset <- "E. coli"
    train2$Type <- "Training"
    cat("训练数据集2加载成功:", nrow(train2), "个化合物\n")
    
    # 统一列名
    train1_clean <- train1 %>% select(Smiles, Activity, Dataset, Type)
    train2_clean <- train2 %>% select(Smiles, Activity, Dataset, Type)
    
    # 加载天然产物数据并抽样
    natural_products <- list()
    
    # COCONUT数据
    tryCatch({
        coconut <- read_csv("天然产物/clean-coconut_filtered_with_weight_under_500-10-2024.csv", 
                           show_col_types = FALSE)
        # 随机抽样2%
        sample_size <- min(8000, floor(nrow(coconut) * 0.02))
        coconut_sample <- coconut %>% 
            slice_sample(n = sample_size) %>%
            rename(Smiles = canonical_smiles) %>%
            mutate(Dataset = "COCONUT",
                   Type = "Natural Products",
                   Activity = -1) %>%
            select(Smiles, Activity, Dataset, Type)
        
        natural_products[[1]] <- coconut_sample
        cat("COCONUT抽样完成:", nrow(coconut_sample), "个化合物 (原始:", nrow(coconut), ")\n")
    }, error = function(e) {
        cat("COCONUT数据处理失败:", e$message, "\n")
    })
    
    # ZINC数据
    tryCatch({
        zinc <- read_csv("天然产物/merged_zinc_data.csv", show_col_types = FALSE)
        # 随机抽样5%
        sample_size <- min(8000, floor(nrow(zinc) * 0.05))
        zinc_sample <- zinc %>% 
            slice_sample(n = sample_size) %>%
            rename(Smiles = smiles) %>%
            mutate(Dataset = "ZINC",
                   Type = "Natural Products",
                   Activity = -1) %>%
            select(Smiles, Activity, Dataset, Type)
        
        natural_products[[2]] <- zinc_sample
        cat("ZINC抽样完成:", nrow(zinc_sample), "个化合物 (原始:", nrow(zinc), ")\n")
    }, error = function(e) {
        cat("ZINC数据处理失败:", e$message, "\n")
    })
    
    # 合并所有数据
    all_datasets <- c(list(train1_clean, train2_clean), natural_products)
    combined_data <- bind_rows(all_datasets)
    
    # 数据清洗
    combined_data <- combined_data %>%
        filter(!is.na(Smiles), nchar(Smiles) > 5)
    
    cat("最终数据集:", nrow(combined_data), "个化合物\n")
    return(combined_data)
}

# 计算分子特征
calculate_features <- function(smiles_vector) {
    cat("计算分子特征...\n")
    
    features <- data.frame(
        Length = nchar(smiles_vector),
        Carbon = stringr::str_count(smiles_vector, "C"),
        Nitrogen = stringr::str_count(smiles_vector, "N"),
        Oxygen = stringr::str_count(smiles_vector, "O"),
        Sulfur = stringr::str_count(smiles_vector, "S"),
        Rings = stringr::str_count(smiles_vector, "[0-9]"),
        Aromatic = stringr::str_count(smiles_vector, "[cnos]"),
        DoubleBonds = stringr::str_count(smiles_vector, "="),
        Stereo = stringr::str_count(smiles_vector, "@"),
        Branches = stringr::str_count(smiles_vector, "\\(")
    )
    
    # 处理缺失值
    features[is.na(features)] <- 0
    
    return(features)
}

# 执行PCA分析
perform_pca_analysis <- function(data, features) {
    cat("执行PCA降维分析...\n")
    
    # 标准化特征
    features_scaled <- scale(features)
    
    # 执行PCA
    pca_result <- prcomp(features_scaled, center = FALSE, scale. = FALSE)
    
    # 计算解释方差
    var_explained <- summary(pca_result)$importance[2, 1:2] * 100
    cat("PC1解释方差:", round(var_explained[1], 2), "%\n")
    cat("PC2解释方差:", round(var_explained[2], 2), "%\n")
    cat("总解释方差:", round(sum(var_explained), 2), "%\n")
    
    # 添加PCA坐标
    data$PC1 <- pca_result$x[, 1]
    data$PC2 <- pca_result$x[, 2]
    
    return(list(data = data, var_explained = var_explained))
}

# 计算重叠度指标
calculate_overlap_metrics <- function(data) {
    cat("计算化学空间重叠度...\n")
    
    training_data <- data %>% filter(Type == "Training")
    np_data <- data %>% filter(Type == "Natural Products")
    
    if (nrow(np_data) == 0) {
        return(list(overlap_ratio = 0, novelty_ratio = 100))
    }
    
    # 计算训练数据的化学空间边界
    train_pc1_range <- range(training_data$PC1)
    train_pc2_range <- range(training_data$PC2)
    
    # 扩展边界
    pc1_margin <- diff(train_pc1_range) * 0.1
    pc2_margin <- diff(train_pc2_range) * 0.1
    
    # 计算天然产物在扩展训练空间内的比例
    np_in_space <- np_data %>%
        filter(PC1 >= train_pc1_range[1] - pc1_margin,
               PC1 <= train_pc1_range[2] + pc1_margin,
               PC2 >= train_pc2_range[1] - pc2_margin,
               PC2 <= train_pc2_range[2] + pc2_margin)
    
    overlap_ratio <- nrow(np_in_space) / nrow(np_data) * 100
    novelty_ratio <- 100 - overlap_ratio
    
    cat("化学空间重叠度:", round(overlap_ratio, 2), "%\n")
    cat("新颖性比例:", round(novelty_ratio, 2), "%\n")
    
    return(list(
        overlap_ratio = overlap_ratio,
        novelty_ratio = novelty_ratio,
        overlapping_count = nrow(np_in_space),
        total_np_count = nrow(np_data),
        training_count = nrow(training_data)
    ))
}

# 创建可视化图表
create_visualizations <- function(data, var_explained, metrics) {
    cat("创建可视化图表...\n")
    
    # 轴标签
    pc1_label <- paste0("PC1 (", round(var_explained[1], 1), "% variance)")
    pc2_label <- paste0("PC2 (", round(var_explained[2], 1), "% variance)")
    
    # 图1: 按类型分类
    p1 <- ggplot(data, aes(x = PC1, y = PC2, color = Type)) +
        geom_point(size = 0.8, alpha = 0.7) +
        scale_color_manual(values = wong_colors[1:2]) +
        labs(title = "Chemical Space by Data Type",
             x = pc1_label, y = pc2_label) +
        theme_nature() +
        guides(color = guide_legend(override.aes = list(size = 2, alpha = 1)))
    
    # 图2: 按数据集分类
    p2 <- ggplot(data, aes(x = PC1, y = PC2, color = Dataset)) +
        geom_point(size = 0.8, alpha = 0.7) +
        scale_color_manual(values = wong_colors[1:length(unique(data$Dataset))]) +
        labs(title = "Chemical Space by Dataset",
             x = pc1_label, y = pc2_label) +
        theme_nature() +
        guides(color = guide_legend(override.aes = list(size = 2, alpha = 1))) +
        theme(legend.text = element_text(size = 7))
    
    # 图3: 分子长度分布 - 添加Length列到数据中
    data_with_length <- data %>%
        mutate(Length = nchar(Smiles))

    p3 <- ggplot(data_with_length, aes(x = Length, fill = Type)) +
        geom_histogram(bins = 30, alpha = 0.7, position = "identity") +
        scale_fill_manual(values = wong_colors[1:2]) +
        labs(title = "Molecular Complexity Distribution",
             x = "SMILES Length", y = "Count") +
        theme_nature()
    
    # 图4: 重叠度统计
    overlap_data <- data.frame(
        Category = c("Overlapping\nwith Training", "Novel\nCompounds"),
        Percentage = c(metrics$overlap_ratio, metrics$novelty_ratio)
    )
    
    p4 <- ggplot(overlap_data, aes(x = Category, y = Percentage, fill = Category)) +
        geom_bar(stat = "identity", color = "black", linewidth = 0.5) +
        scale_fill_manual(values = wong_colors[3:4]) +
        geom_text(aes(label = paste0(round(Percentage, 1), "%")), 
                 vjust = -0.5, size = 3) +
        labs(title = "Chemical Space Overlap Statistics",
             x = "", y = "Percentage (%)") +
        theme_nature() +
        theme(legend.position = "none") +
        ylim(0, max(overlap_data$Percentage) * 1.1)
    
    # 组合图表
    combined_plot <- grid.arrange(p1, p2, p3, p4, ncol = 2, nrow = 2,
                                 top = textGrob("Chemical Space Overlap Analysis: Natural Products vs Training Data",
                                               gp = gpar(fontsize = 12)))
    
    # 保存图表
    ggsave("Chemical_Space_Overlap_R.pdf", combined_plot, 
           width = 183, height = 120, units = "mm", device = "pdf")
    ggsave("Chemical_Space_Overlap_R.png", combined_plot, 
           width = 183, height = 120, units = "mm", dpi = 300)
    
    cat("图表已保存: Chemical_Space_Overlap_R.pdf/.png\n")
    
    return(list(p1 = p1, p2 = p2, p3 = p3, p4 = p4))
}

# 主函数
main <- function() {
    cat("开始化学空间重叠度分析\n")
    cat(rep("=", 50), "\n")
    
    # 加载和抽样数据
    data <- load_and_sample_data()
    
    # 计算分子特征
    features <- calculate_features(data$Smiles)
    
    # 执行PCA分析
    pca_results <- perform_pca_analysis(data, features)
    analyzed_data <- pca_results$data
    var_explained <- pca_results$var_explained
    
    # 计算重叠度指标
    metrics <- calculate_overlap_metrics(analyzed_data)
    
    # 创建可视化
    plots <- create_visualizations(analyzed_data, var_explained, metrics)
    
    # 保存结果
    results_df <- data.frame(
        Metric = c("Training_Count", "Natural_Products_Count", "Overlap_Ratio", 
                  "Novelty_Ratio", "PC1_Variance", "PC2_Variance", "Total_Variance"),
        Value = c(metrics$training_count, metrics$total_np_count, 
                 metrics$overlap_ratio, metrics$novelty_ratio,
                 var_explained[1], var_explained[2], sum(var_explained))
    )
    
    write.csv(results_df, "Overlap_Analysis_Results_R.csv", row.names = FALSE)
    
    # 输出总结
    cat("\n", rep("=", 50), "\n")
    cat("化学空间重叠度分析完成\n")
    cat(rep("=", 50), "\n")
    cat("训练数据化合物:", metrics$training_count, "\n")
    cat("天然产物化合物:", metrics$total_np_count, "\n")
    cat("化学空间重叠度:", round(metrics$overlap_ratio, 2), "%\n")
    cat("新颖化合物比例:", round(metrics$novelty_ratio, 2), "%\n")
    cat("PCA总解释方差:", round(sum(var_explained), 2), "%\n")
    cat("结果已保存至: Overlap_Analysis_Results_R.csv\n")
}

# 执行主函数
main()
