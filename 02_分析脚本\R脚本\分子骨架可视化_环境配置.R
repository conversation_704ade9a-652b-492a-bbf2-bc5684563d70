# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-14
# 描述: 分子骨架可视化环境配置 - 检查和安装必要的R包

# 设置CRAN镜像
options(repos = c(CRAN = "https://cran.rstudio.com/"))

# 定义需要的包列表
required_packages <- c(
    # 基础绘图和数据处理
    "ggplot2",
    "dplyr", 
    "readr",
    "gridExtra",
    "grid",
    "stringr",
    
    # 化学信息学包
    "ChemmineR",
    "rcdk",
    "rJava",
    
    # 图像处理和输出
    "png",
    "grDevices",
    "Cairo",
    
    # 其他实用包
    "scales",
    "RColorBrewer"
)

# 检查包安装状态的函数
check_and_install_packages <- function(packages) {
    cat("=== 分子骨架可视化环境配置 ===\n")
    cat("检查和安装必要的R包...\n\n")
    
    installed_packages <- installed.packages()[,"Package"]
    missing_packages <- packages[!packages %in% installed_packages]
    
    if (length(missing_packages) > 0) {
        cat("需要安装的包:\n")
        for (pkg in missing_packages) {
            cat("  -", pkg, "\n")
        }
        cat("\n")
        
        # 安装缺失的包
        for (pkg in missing_packages) {
            cat("正在安装:", pkg, "...\n")
            tryCatch({
                if (pkg %in% c("ChemmineR", "rcdk")) {
                    # 化学信息学包可能需要特殊处理
                    if (pkg == "ChemmineR") {
                        if (!requireNamespace("BiocManager", quietly = TRUE)) {
                            install.packages("BiocManager")
                        }
                        BiocManager::install("ChemmineR")
                    } else if (pkg == "rcdk") {
                        # rcdk需要Java环境
                        install.packages("rcdk")
                    }
                } else {
                    install.packages(pkg, dependencies = TRUE)
                }
                cat("  ✓", pkg, "安装成功\n")
            }, error = function(e) {
                cat("  ✗", pkg, "安装失败:", e$message, "\n")
            })
        }
    } else {
        cat("所有必要的包都已安装。\n")
    }
    
    cat("\n=== 包加载测试 ===\n")
    # 测试包加载
    success_count <- 0
    for (pkg in packages) {
        tryCatch({
            library(pkg, character.only = TRUE, quietly = TRUE)
            cat("  ✓", pkg, "加载成功\n")
            success_count <- success_count + 1
        }, error = function(e) {
            cat("  ✗", pkg, "加载失败:", e$message, "\n")
        })
    }
    
    cat("\n=== 配置总结 ===\n")
    cat("成功加载包数量:", success_count, "/", length(packages), "\n")
    
    if (success_count == length(packages)) {
        cat("✓ 环境配置完成，可以开始分子骨架可视化！\n")
        return(TRUE)
    } else {
        cat("⚠ 部分包配置失败，可能影响功能使用\n")
        return(FALSE)
    }
}

# 执行配置检查
config_success <- check_and_install_packages(required_packages)

# 如果配置成功，进行额外的环境测试
if (config_success) {
    cat("\n=== 化学信息学功能测试 ===\n")
    
    # 测试基本SMILES处理（不依赖外部包）
    test_smiles <- "c1ccccc1"  # 苯环
    cat("测试SMILES:", test_smiles, "\n")
    
    # 基本字符串分析
    smiles_length <- nchar(test_smiles)
    carbon_count <- stringr::str_count(test_smiles, "c|C")
    cat("SMILES长度:", smiles_length, "\n")
    cat("碳原子数量:", carbon_count, "\n")
    
    cat("✓ 基础功能测试通过\n")
}

cat("\n=== 配置完成 ===\n")
