# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-11
# 描述: 论文级化学空间重叠度分析图表

# 加载必要的包
suppressMessages({
    library(ggplot2)
    library(dplyr)
    library(readr)
    library(gridExtra)
    library(grid)
    library(scales)
    library(RColorBrewer)
})

# 设置随机种子
set.seed(42)

# Nature期刊标准主题
theme_publication <- function(base_size = 7, base_family = "") {
    theme_bw(base_size = base_size, base_family = base_family) +
        theme(
            # 移除网格线
            panel.grid.major = element_blank(),
            panel.grid.minor = element_blank(),
            
            # 设置边框
            panel.border = element_rect(colour = "black", fill = NA, linewidth = 0.5),
            
            # 标题和文本
            plot.title = element_text(hjust = 0.5, size = rel(1.2), face = "bold"),
            plot.subtitle = element_text(hjust = 0.5, size = rel(0.9)),
            
            # 坐标轴
            axis.text = element_text(color = "black", size = rel(0.9)),
            axis.title = element_text(color = "black", size = rel(1.0), face = "bold"),
            axis.ticks = element_line(colour = "black", linewidth = 0.5),
            axis.line = element_line(colour = "black", linewidth = 0.5),
            
            # 图例
            legend.key = element_blank(),
            legend.background = element_blank(),
            legend.title = element_text(face = "bold"),
            legend.text = element_text(size = rel(0.8)),
            
            # 分面
            strip.background = element_rect(fill = "white", colour = "black", linewidth = 0.5),
            strip.text = element_text(face = "bold"),
            
            # 边距
            plot.margin = margin(5, 5, 5, 5, "mm")
        )
}

# Wong配色方案 (色盲友好)
wong_colors <- c("#000000", "#E69F00", "#56B4E9", "#009E73",
                 "#F0E442", "#0072B2", "#D55E00", "#CC79A7")

# 加载分析结果数据
load_analysis_data <- function() {
    cat("正在重新加载分析数据...\n")
    
    # 加载训练数据集
    train1 <- read_csv("cleaned_data-12-9-no-duplicate.csv", show_col_types = FALSE)
    train1$Dataset <- "S. aureus Training"
    train1$Type <- "Training"
    train1$Species <- "S. aureus"
    
    train2 <- read_csv("filtered_ecoli_9000-nosalt.csv", show_col_types = FALSE)
    train2$Dataset <- "E. coli Training"
    train2$Type <- "Training"
    train2$Species <- "E. coli"
    
    # 统一列名
    train1_clean <- train1 %>% select(Smiles, Activity, Dataset, Type, Species)
    train2_clean <- train2 %>% select(Smiles, Activity, Dataset, Type, Species)
    
    # 加载天然产物数据并抽样
    coconut <- read_csv("天然产物/clean-coconut_filtered_with_weight_under_500-10-2024.csv", 
                       show_col_types = FALSE)
    coconut_sample <- coconut %>% 
        slice_sample(n = 8000) %>%
        rename(Smiles = canonical_smiles) %>%
        mutate(Dataset = "COCONUT Natural Products",
               Type = "Natural Products",
               Species = "Natural Products",
               Activity = -1) %>%
        select(Smiles, Activity, Dataset, Type, Species)
    
    zinc <- read_csv("天然产物/merged_zinc_data.csv", show_col_types = FALSE)
    zinc_sample <- zinc %>% 
        slice_sample(n = 8000) %>%
        rename(Smiles = smiles) %>%
        mutate(Dataset = "ZINC Natural Products",
               Type = "Natural Products", 
               Species = "Natural Products",
               Activity = -1) %>%
        select(Smiles, Activity, Dataset, Type, Species)
    
    # 合并数据
    combined_data <- bind_rows(train1_clean, train2_clean, coconut_sample, zinc_sample)
    combined_data <- combined_data %>%
        filter(!is.na(Smiles), nchar(Smiles) > 5)
    
    # 计算分子特征
    features <- data.frame(
        Length = nchar(combined_data$Smiles),
        Carbon = stringr::str_count(combined_data$Smiles, "C"),
        Nitrogen = stringr::str_count(combined_data$Smiles, "N"),
        Oxygen = stringr::str_count(combined_data$Smiles, "O"),
        Sulfur = stringr::str_count(combined_data$Smiles, "S"),
        Rings = stringr::str_count(combined_data$Smiles, "[0-9]"),
        Aromatic = stringr::str_count(combined_data$Smiles, "[cnos]"),
        DoubleBonds = stringr::str_count(combined_data$Smiles, "="),
        Stereo = stringr::str_count(combined_data$Smiles, "@"),
        Branches = stringr::str_count(combined_data$Smiles, "\\(")
    )
    
    # PCA分析
    features_scaled <- scale(features)
    pca_result <- prcomp(features_scaled, center = FALSE, scale. = FALSE)
    var_explained <- summary(pca_result)$importance[2, 1:2] * 100
    
    # 添加PCA坐标
    combined_data$PC1 <- pca_result$x[, 1]
    combined_data$PC2 <- pca_result$x[, 2]
    combined_data$Length <- features$Length
    
    cat("数据加载完成:", nrow(combined_data), "个化合物\n")
    cat("PC1解释方差:", round(var_explained[1], 2), "%\n")
    cat("PC2解释方差:", round(var_explained[2], 2), "%\n")
    
    return(list(data = combined_data, var_explained = var_explained))
}

# 创建主要化学空间分布图 (Figure 1)
create_figure1_chemical_space <- function(data, var_explained) {
    cat("创建Figure 1: 化学空间分布图...\n")
    
    # 轴标签
    pc1_label <- paste0("PC1 (", round(var_explained[1], 1), "% variance)")
    pc2_label <- paste0("PC2 (", round(var_explained[2], 1), "% variance)")
    
    # 创建分面图
    p1 <- ggplot(data, aes(x = PC1, y = PC2, color = Type)) +
        geom_point(size = 0.3, alpha = 0.6) +
        scale_color_manual(values = c("Training" = wong_colors[1], 
                                     "Natural Products" = wong_colors[2]),
                          name = "Dataset Type") +
        labs(title = "Chemical Space Distribution",
             subtitle = "Training Data vs Natural Product Libraries",
             x = pc1_label, y = pc2_label) +
        theme_publication() +
        guides(color = guide_legend(override.aes = list(size = 2, alpha = 1))) +
        theme(legend.position = "bottom")
    
    # 保存单独图表
    ggsave("Figure1_Chemical_Space_Distribution.pdf", p1, 
           width = 89, height = 80, units = "mm", device = "pdf")
    ggsave("Figure1_Chemical_Space_Distribution.png", p1, 
           width = 89, height = 80, units = "mm", dpi = 300)
    
    return(p1)
}

# 创建详细数据集对比图 (Figure 2)
create_figure2_dataset_comparison <- function(data, var_explained) {
    cat("创建Figure 2: 数据集详细对比图...\n")
    
    pc1_label <- paste0("PC1 (", round(var_explained[1], 1), "% variance)")
    pc2_label <- paste0("PC2 (", round(var_explained[2], 1), "% variance)")
    
    # 按数据集分类的化学空间
    p2a <- ggplot(data, aes(x = PC1, y = PC2, color = Dataset)) +
        geom_point(size = 0.3, alpha = 0.6) +
        scale_color_manual(values = wong_colors[1:4],
                          name = "Dataset") +
        labs(title = "Chemical Space by Dataset",
             x = pc1_label, y = pc2_label) +
        theme_publication() +
        guides(color = guide_legend(override.aes = list(size = 2, alpha = 1))) +
        theme(legend.position = "bottom",
              legend.text = element_text(size = 6))
    
    # 分子复杂性分布对比
    p2b <- ggplot(data, aes(x = Length, fill = Type)) +
        geom_histogram(bins = 40, alpha = 0.7, position = "identity") +
        scale_fill_manual(values = wong_colors[1:2],
                         name = "Dataset Type") +
        labs(title = "Molecular Complexity Distribution",
             x = "SMILES Length (characters)", y = "Frequency") +
        theme_publication() +
        theme(legend.position = "bottom")
    
    # 组合图表
    combined_p2 <- grid.arrange(p2a, p2b, ncol = 2,
                               top = textGrob("Dataset Comparison Analysis",
                                             gp = gpar(fontsize = 10, fontface = "bold")))
    
    # 保存图表
    ggsave("Figure2_Dataset_Comparison.pdf", combined_p2, 
           width = 183, height = 80, units = "mm", device = "pdf")
    ggsave("Figure2_Dataset_Comparison.png", combined_p2, 
           width = 183, height = 80, units = "mm", dpi = 300)
    
    return(list(p2a = p2a, p2b = p2b))
}

# 创建重叠度分析图 (Figure 3)
create_figure3_overlap_analysis <- function(data) {
    cat("创建Figure 3: 重叠度分析图...\n")
    
    # 计算重叠度统计
    training_data <- data %>% filter(Type == "Training")
    np_data <- data %>% filter(Type == "Natural Products")
    
    # 计算边界
    train_pc1_range <- range(training_data$PC1)
    train_pc2_range <- range(training_data$PC2)
    pc1_margin <- diff(train_pc1_range) * 0.1
    pc2_margin <- diff(train_pc2_range) * 0.1
    
    # 标记重叠区域
    np_data$Overlap_Status <- ifelse(
        np_data$PC1 >= train_pc1_range[1] - pc1_margin &
        np_data$PC1 <= train_pc1_range[2] + pc1_margin &
        np_data$PC2 >= train_pc2_range[1] - pc2_margin &
        np_data$PC2 <= train_pc2_range[2] + pc2_margin,
        "Overlapping", "Novel"
    )
    
    overlap_ratio <- sum(np_data$Overlap_Status == "Overlapping") / nrow(np_data) * 100
    
    # 重叠度可视化
    p3a <- ggplot() +
        # 训练数据背景
        geom_point(data = training_data, aes(x = PC1, y = PC2), 
                  color = wong_colors[1], size = 0.2, alpha = 0.3) +
        # 天然产物按重叠状态着色
        geom_point(data = np_data, aes(x = PC1, y = PC2, color = Overlap_Status), 
                  size = 0.4, alpha = 0.7) +
        scale_color_manual(values = c("Overlapping" = wong_colors[3], 
                                     "Novel" = wong_colors[7]),
                          name = "Natural Products") +
        labs(title = "Chemical Space Overlap Analysis",
             subtitle = paste0("Overlap: ", round(overlap_ratio, 1), "%"),
             x = "PC1", y = "PC2") +
        theme_publication() +
        theme(legend.position = "bottom")
    
    # 重叠度统计柱状图
    overlap_stats <- data.frame(
        Category = c("Overlapping\nwith Training", "Novel\nCompounds"),
        Percentage = c(overlap_ratio, 100 - overlap_ratio),
        Count = c(sum(np_data$Overlap_Status == "Overlapping"),
                 sum(np_data$Overlap_Status == "Novel"))
    )
    
    p3b <- ggplot(overlap_stats, aes(x = Category, y = Percentage, fill = Category)) +
        geom_bar(stat = "identity", color = "black", linewidth = 0.5, width = 0.6) +
        scale_fill_manual(values = wong_colors[3:4]) +
        geom_text(aes(label = paste0(round(Percentage, 1), "%\n(n=", 
                                   format(Count, big.mark = ","), ")")), 
                 vjust = -0.5, size = 3, fontface = "bold") +
        labs(title = "Overlap Statistics",
             x = "", y = "Percentage (%)") +
        theme_publication() +
        theme(legend.position = "none",
              axis.text.x = element_text(size = 8)) +
        ylim(0, max(overlap_stats$Percentage) * 1.2)
    
    # 组合图表
    combined_p3 <- grid.arrange(p3a, p3b, ncol = 2, widths = c(2, 1),
                               top = textGrob("Chemical Space Overlap Analysis",
                                             gp = gpar(fontsize = 10, fontface = "bold")))
    
    # 保存图表
    ggsave("Figure3_Overlap_Analysis.pdf", combined_p3, 
           width = 183, height = 80, units = "mm", device = "pdf")
    ggsave("Figure3_Overlap_Analysis.png", combined_p3, 
           width = 183, height = 80, units = "mm", dpi = 300)
    
    return(list(p3a = p3a, p3b = p3b, stats = overlap_stats))
}

# 创建物种特异性分析图 (Figure 4)
create_figure4_species_analysis <- function(data, var_explained) {
    cat("创建Figure 4: 物种特异性分析图...\n")
    
    pc1_label <- paste0("PC1 (", round(var_explained[1], 1), "% variance)")
    pc2_label <- paste0("PC2 (", round(var_explained[2], 1), "% variance)")
    
    # 按物种分面的化学空间
    training_data <- data %>% filter(Type == "Training")
    
    p4 <- ggplot(training_data, aes(x = PC1, y = PC2, color = factor(Activity))) +
        geom_point(size = 0.5, alpha = 0.7) +
        scale_color_manual(values = wong_colors[3:4],
                          name = "Activity",
                          labels = c("Inactive", "Active")) +
        labs(title = "Species-Specific Chemical Space",
             subtitle = "Training Data Activity Distribution",
             x = pc1_label, y = pc2_label) +
        theme_publication() +
        facet_wrap(~Species, scales = "free") +
        theme(legend.position = "bottom",
              strip.text = element_text(size = 8, face = "bold"))
    
    # 保存图表
    ggsave("Figure4_Species_Analysis.pdf", p4, 
           width = 183, height = 90, units = "mm", device = "pdf")
    ggsave("Figure4_Species_Analysis.png", p4, 
           width = 183, height = 90, units = "mm", dpi = 300)
    
    return(p4)
}

# 创建综合总结图 (Figure 5)
create_figure5_summary <- function(data, var_explained, overlap_stats) {
    cat("创建Figure 5: 综合总结图...\n")
    
    # 数据集规模对比
    dataset_summary <- data %>%
        group_by(Dataset, Type) %>%
        summarise(Count = n(), .groups = 'drop') %>%
        mutate(Dataset_Short = case_when(
            Dataset == "S. aureus Training" ~ "S. aureus\nTraining",
            Dataset == "E. coli Training" ~ "E. coli\nTraining", 
            Dataset == "COCONUT Natural Products" ~ "COCONUT\nNatural Products",
            Dataset == "ZINC Natural Products" ~ "ZINC\nNatural Products"
        ))
    
    p5a <- ggplot(dataset_summary, aes(x = Dataset_Short, y = Count, fill = Type)) +
        geom_bar(stat = "identity", color = "black", linewidth = 0.5) +
        scale_fill_manual(values = wong_colors[1:2]) +
        geom_text(aes(label = format(Count, big.mark = ",")), 
                 vjust = -0.5, size = 3, fontface = "bold") +
        labs(title = "Dataset Composition",
             x = "", y = "Number of Compounds") +
        theme_publication() +
        theme(legend.position = "bottom",
              axis.text.x = element_text(size = 7, angle = 45, hjust = 1)) +
        scale_y_continuous(labels = comma_format())
    
    # PCA解释方差
    pca_data <- data.frame(
        Component = c("PC1", "PC2", "Remaining"),
        Variance = c(var_explained[1], var_explained[2], 100 - sum(var_explained))
    )
    
    p5b <- ggplot(pca_data, aes(x = "", y = Variance, fill = Component)) +
        geom_bar(stat = "identity", width = 1, color = "black", linewidth = 0.5) +
        coord_polar("y", start = 0) +
        scale_fill_manual(values = wong_colors[c(5, 6, 8)]) +
        geom_text(aes(label = paste0(round(Variance, 1), "%")), 
                 position = position_stack(vjust = 0.5), size = 3, fontface = "bold") +
        labs(title = "PCA Variance Explained") +
        theme_publication() +
        theme(axis.text = element_blank(),
              axis.ticks = element_blank(),
              axis.title = element_blank(),
              legend.position = "bottom")
    
    # 组合图表
    combined_p5 <- grid.arrange(p5a, p5b, ncol = 2,
                               top = textGrob("Dataset Summary and PCA Analysis",
                                             gp = gpar(fontsize = 10, fontface = "bold")))
    
    # 保存图表
    ggsave("Figure5_Summary.pdf", combined_p5, 
           width = 183, height = 80, units = "mm", device = "pdf")
    ggsave("Figure5_Summary.png", combined_p5, 
           width = 183, height = 80, units = "mm", dpi = 300)
    
    return(list(p5a = p5a, p5b = p5b))
}

# 主函数
main <- function() {
    cat("开始创建论文级化学空间分析图表\n")
    cat(rep("=", 60), "\n")
    
    # 加载数据
    analysis_results <- load_analysis_data()
    data <- analysis_results$data
    var_explained <- analysis_results$var_explained
    
    # 创建各个图表
    fig1 <- create_figure1_chemical_space(data, var_explained)
    fig2 <- create_figure2_dataset_comparison(data, var_explained)
    fig3 <- create_figure3_overlap_analysis(data)
    fig4 <- create_figure4_species_analysis(data, var_explained)
    fig5 <- create_figure5_summary(data, var_explained, fig3$stats)
    
    cat("\n", rep("=", 60), "\n")
    cat("论文级图表创建完成！\n")
    cat("生成的图表文件:\n")
    cat("- Figure1_Chemical_Space_Distribution.pdf/.png\n")
    cat("- Figure2_Dataset_Comparison.pdf/.png\n")
    cat("- Figure3_Overlap_Analysis.pdf/.png\n")
    cat("- Figure4_Species_Analysis.pdf/.png\n")
    cat("- Figure5_Summary.pdf/.png\n")
    cat("\n所有图表均符合Nature期刊发表标准\n")
}

# 执行主函数
main()
