---
description:
globs:
alwaysApply: false
---
name: 中国风图表规范
description: 提供在R中创建具有中国传统美学风格的学术图表的指南，同时确保其符合学术发表的清晰度和严谨性要求。
---
# 中国风学术图表规范

## 1. 核心理念
本规范旨在将中国传统审美与现代科学数据可视化相结合，创造出既有文化底蕴又不失专业严谨的图表。核心在于**色彩的雅致**和**构图的和谐**，同时必须**兼容色盲友好**原则。

## 2. 推荐配色方案 (学术优化版)
此配色方案从中国传统国画和瓷器中汲取灵感，并调整了明度和饱和度，以确保信息传递的清晰度和对色觉障碍人士的友好性。

- **核心配色 (8色)**:
    - **朱红 (Primary)**: `#C0392B` (用于关键数据、强调项)
    - **靛蓝 (Secondary)**: `#2980B9` (用于次要数据或对比项)
    - **天青 (Tertiary)**: `#73C6B6` (柔和的青色，用于对照组或背景)
    - **定窑白 (Background/Highlight)**: `#FDFEFE` (作为背景色)
    - **赭石 (Accent 1)**: `#A0522D` (暖色调，用于分组)
    - **石绿 (Accent 2)**: `#27AE60` (鲜明的绿色，谨慎与红色搭配使用)
    - **藤黄 (Accent 3)**: `#F1C40F` (用于高亮显示)
    - **青金 (Accent 4)**: `#34495E` (深沉的蓝色，用于文本和坐标轴)

- **R语言向量**:
```R
chinese_academic_colors <- c(
  "#C0392B", "#2980B9", "#27AE60", "#F1C40F",
  "#A0522D", "#34495E", "#73C6B6", "#E74C3C" # 备用红色
)
```

## 3. 字体选择 (Typography)
- **英文**: **Times New Roman** 或 **Georgia**。这类衬线字体能更好地衬托古典风格。
- **中文 (若需)**: **思源宋体 (Source Han Serif)** 或 **方正宋刻本秀楷**。宋体类字体具有雕版印刷的古典美感。

## 4. 设计元素与构图
- **留白 (White Space)**: 参考中国山水画的构图，在图表元素之间保留足够的空间，避免拥挤，营造出呼吸感和高级感。
- **线条 (Lines)**:
    - 坐标轴和边框线可使用较深的颜色，如 **青金 (`#34495E`)**。
    - 数据点之间的连接线可使用柔和的曲线 (`geom_smooth`) 代替生硬的直线，但需注明这代表趋势。
- **背景与网格线**:
    - 背景色推荐使用非常淡的米白或天青色，或直接使用白色。
    - **必须禁用网格线**，以突出数据本身，保持画面的纯净。

## 5. R (ggplot2) 实践示例
```R
# 中国风主题函数
theme_chinese_academic <- function(base_size = 12, base_family = "Times New Roman") {
  theme_minimal(base_size = base_size, base_family = base_family) +
  theme(
    plot.title = element_text(hjust = 0.5, size = rel(1.5), color = "#34495E"),
    plot.subtitle = element_text(hjust = 0.5, color = "#34495E"),
    axis.title = element_text(size = rel(1.2), color = "#34495E"),
    axis.text = element_text(color = "#34495E"),
    panel.grid = element_blank(), # 禁用网格线
    axis.line = element_line(color = "#34495E", size = 0.5), # 添加坐标轴线
    legend.title = element_text(color = "#34495E"),
    legend.text = element_text(color = "#34495E"),
    plot.background = element_rect(fill = "#FDFEFE", color = NA) # 设置淡雅背景
  )
}

# 使用示例
# ggplot(data, aes(x = x_var, y = y_var, fill = group)) +
#   geom_violin() + # 提琴图等形状优美的图表很适合中国风
#   scale_fill_manual(values = chinese_academic_colors) +
#   labs(title = "圖表標題", subtitle = "副標題", x = "X軸標籤", y = "Y軸標籤") +
#   theme_chinese_academic()
```
本规范提供的是一个融合方向，最终效果需在保持科学准确性的前提下进行艺术调整。
