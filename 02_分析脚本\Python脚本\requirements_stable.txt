# 分子骨架真实结构图生成器 - 稳定环境依赖
# 作者: ZK
# 日期: 2025-07-14
# 描述: 最小依赖配置，避免numpy版本冲突

# 核心依赖
rdkit>=2023.3.1
Pillow>=9.0.0

# 可选依赖（如果需要更多功能）
# matplotlib>=3.5.0  # 仅在需要额外绘图功能时安装
# numpy<2.0.0        # 如果遇到版本冲突，可以限制numpy版本

# 安装说明：
# 1. 创建虚拟环境（推荐）：
#    python -m venv venv_molecular
#    venv_molecular\Scripts\activate  # Windows
#    source venv_molecular/bin/activate  # Linux/Mac
#
# 2. 安装依赖：
#    pip install -r requirements_stable.txt
#
# 3. 或者使用conda（推荐用于RDKit）：
#    conda create -n molecular python=3.9
#    conda activate molecular
#    conda install -c conda-forge rdkit
#    pip install Pillow
