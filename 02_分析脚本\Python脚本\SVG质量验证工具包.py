# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-14
# 描述: SVG质量验证工具包 - 确保生成真正可编辑的SVG文件

import xml.etree.ElementTree as ET
import os
import re
from typing import Dict, List, Tuple, Optional

class SVGQualityValidator:
    """SVG质量验证器 - 基于真正可编辑SVG生成规则"""
    
    def __init__(self):
        self.quality_standards = {
            'min_paths': 5,           # 最少路径数量
            'min_groups': 2,          # 最少组数量
            'min_texts': 1,           # 最少文本数量
            'max_file_size': 500000,  # 最大文件大小(字节)
            'max_images': 0           # 最大嵌入图像数量(应为0)
        }
    
    def validate_file(self, svg_file: str) -> Tuple[bool, Dict]:
        """验证SVG文件质量"""
        print(f"=== SVG质量验证：{svg_file} ===")
        
        if not os.path.exists(svg_file):
            return False, {'error': '文件不存在'}
        
        try:
            # 基础文件信息
            file_size = os.path.getsize(svg_file)
            print(f"文件大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
            
            # 解析SVG
            tree = ET.parse(svg_file)
            root = tree.getroot()
            
            # 统计元素
            stats = self._count_elements(root)
            
            # 质量检查
            quality_checks = self._perform_quality_checks(file_size, stats)
            
            # 可编辑性检查
            editability_checks = self._check_editability(root)
            
            # 综合结果
            all_checks = {**quality_checks, **editability_checks}
            is_valid = all(all_checks.values())
            
            # 输出结果
            self._print_results(stats, all_checks, is_valid)
            
            return is_valid, {
                'file_size': file_size,
                'stats': stats,
                'checks': all_checks,
                'is_valid': is_valid
            }
            
        except Exception as e:
            error_msg = f"验证失败: {str(e)}"
            print(f"✗ {error_msg}")
            return False, {'error': error_msg}
    
    def _count_elements(self, root) -> Dict:
        """统计SVG元素数量"""
        ns = {'svg': 'http://www.w3.org/2000/svg'}
        
        stats = {
            'paths': len(root.findall('.//svg:path', ns)),
            'groups': len(root.findall('.//svg:g', ns)),
            'texts': len(root.findall('.//svg:text', ns)),
            'rects': len(root.findall('.//svg:rect', ns)),
            'circles': len(root.findall('.//svg:circle', ns)),
            'ellipses': len(root.findall('.//svg:ellipse', ns)),
            'lines': len(root.findall('.//svg:line', ns)),
            'polylines': len(root.findall('.//svg:polyline', ns)),
            'polygons': len(root.findall('.//svg:polygon', ns)),
            'images': len(root.findall('.//svg:image', ns)),
            'foreign_objects': len(root.findall('.//svg:foreignObject', ns))
        }
        
        # 计算总矢量元素数量
        stats['total_vector_elements'] = (
            stats['paths'] + stats['rects'] + stats['circles'] + 
            stats['ellipses'] + stats['lines'] + stats['polylines'] + 
            stats['polygons']
        )
        
        return stats
    
    def _perform_quality_checks(self, file_size: int, stats: Dict) -> Dict:
        """执行质量检查"""
        return {
            'sufficient_paths': stats['paths'] >= self.quality_standards['min_paths'],
            'sufficient_groups': stats['groups'] >= self.quality_standards['min_groups'],
            'sufficient_texts': stats['texts'] >= self.quality_standards['min_texts'],
            'reasonable_file_size': file_size <= self.quality_standards['max_file_size'],
            'no_embedded_images': stats['images'] <= self.quality_standards['max_images'],
            'no_foreign_objects': stats['foreign_objects'] == 0,
            'has_vector_elements': stats['total_vector_elements'] > 0
        }
    
    def _check_editability(self, root) -> Dict:
        """检查可编辑性特征"""
        checks = {}
        
        # 检查ID命名
        elements_with_ids = root.findall('.//*[@id]')
        checks['has_semantic_ids'] = len(elements_with_ids) > 0
        
        # 检查CSS样式
        style_elements = root.findall('.//svg:style', {'svg': 'http://www.w3.org/2000/svg'})
        defs_elements = root.findall('.//svg:defs', {'svg': 'http://www.w3.org/2000/svg'})
        checks['has_css_styles'] = len(style_elements) > 0 or len(defs_elements) > 0
        
        # 检查元数据
        title_elements = root.findall('.//svg:title', {'svg': 'http://www.w3.org/2000/svg'})
        desc_elements = root.findall('.//svg:desc', {'svg': 'http://www.w3.org/2000/svg'})
        checks['has_metadata'] = len(title_elements) > 0 or len(desc_elements) > 0
        
        # 检查分组结构
        nested_groups = root.findall('.//svg:g//svg:g', {'svg': 'http://www.w3.org/2000/svg'})
        checks['has_logical_grouping'] = len(nested_groups) > 0
        
        return checks
    
    def _print_results(self, stats: Dict, checks: Dict, is_valid: bool):
        """打印验证结果"""
        print("\n--- 元素统计 ---")
        for key, value in stats.items():
            if value > 0:
                print(f"✓ {key}: {value}")
        
        print("\n--- 质量检查 ---")
        for check, passed in checks.items():
            status = "✓" if passed else "✗"
            print(f"{status} {check}: {'通过' if passed else '失败'}")
        
        print(f"\n--- 总体评估 ---")
        if is_valid:
            print("🎉 SVG质量验证通过！这是一个真正可编辑的矢量SVG文件。")
        else:
            print("⚠️ SVG质量验证失败！可能不是真正可编辑的矢量文件。")

class SVGContentAnalyzer:
    """SVG内容分析器"""
    
    @staticmethod
    def detect_embedded_images(svg_file: str) -> List[str]:
        """检测嵌入的图像"""
        with open(svg_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找data:image模式
        image_patterns = re.findall(r'data:image/[^"\']*', content)
        return image_patterns
    
    @staticmethod
    def analyze_path_complexity(svg_file: str) -> Dict:
        """分析路径复杂度"""
        tree = ET.parse(svg_file)
        root = tree.getroot()
        
        paths = root.findall('.//{http://www.w3.org/2000/svg}path')
        
        if not paths:
            return {'total_paths': 0, 'avg_commands': 0, 'complexity': 'none'}
        
        total_commands = 0
        for path in paths:
            d_attr = path.get('d', '')
            # 简单计算命令数量（M, L, C, Z等）
            commands = len(re.findall(r'[MLHVCSQTAZ]', d_attr.upper()))
            total_commands += commands
        
        avg_commands = total_commands / len(paths)
        
        if avg_commands < 5:
            complexity = 'simple'
        elif avg_commands < 15:
            complexity = 'moderate'
        else:
            complexity = 'complex'
        
        return {
            'total_paths': len(paths),
            'total_commands': total_commands,
            'avg_commands': round(avg_commands, 2),
            'complexity': complexity
        }

def batch_validate_svgs(directory: str) -> Dict:
    """批量验证目录中的SVG文件"""
    validator = SVGQualityValidator()
    results = {}
    
    svg_files = [f for f in os.listdir(directory) if f.endswith('.svg')]
    
    print(f"=== 批量验证 {len(svg_files)} 个SVG文件 ===")
    
    for svg_file in svg_files:
        file_path = os.path.join(directory, svg_file)
        is_valid, details = validator.validate_file(file_path)
        results[svg_file] = {
            'is_valid': is_valid,
            'details': details
        }
        print("-" * 50)
    
    # 生成总结报告
    valid_count = sum(1 for r in results.values() if r['is_valid'])
    print(f"\n=== 批量验证总结 ===")
    print(f"总文件数: {len(svg_files)}")
    print(f"验证通过: {valid_count}")
    print(f"验证失败: {len(svg_files) - valid_count}")
    print(f"通过率: {valid_count/len(svg_files)*100:.1f}%")
    
    return results

def generate_quality_report(svg_file: str, output_file: Optional[str] = None) -> str:
    """生成详细的质量报告"""
    validator = SVGQualityValidator()
    analyzer = SVGContentAnalyzer()
    
    # 执行验证
    is_valid, details = validator.validate_file(svg_file)
    
    # 分析内容
    embedded_images = analyzer.detect_embedded_images(svg_file)
    path_analysis = analyzer.analyze_path_complexity(svg_file)
    
    # 生成报告
    report = f"""SVG质量分析报告
{'='*50}
文件: {svg_file}
生成时间: 2025-07-14

基础信息:
- 文件大小: {details.get('file_size', 0):,} 字节
- 验证结果: {'✓ 通过' if is_valid else '✗ 失败'}

元素统计:
"""
    
    if 'stats' in details:
        for key, value in details['stats'].items():
            if value > 0:
                report += f"- {key}: {value}\n"
    
    report += f"""
路径复杂度分析:
- 总路径数: {path_analysis['total_paths']}
- 平均命令数: {path_analysis['avg_commands']}
- 复杂度等级: {path_analysis['complexity']}

质量检查结果:
"""
    
    if 'checks' in details:
        for check, passed in details['checks'].items():
            status = "✓ 通过" if passed else "✗ 失败"
            report += f"- {check}: {status}\n"
    
    if embedded_images:
        report += f"\n⚠️ 检测到 {len(embedded_images)} 个嵌入图像\n"
    
    report += f"""
可编辑性评估:
{'✓ 真正可编辑的矢量SVG' if is_valid else '✗ 可能不是真正可编辑的矢量SVG'}

建议:
"""
    
    if not is_valid:
        report += "- 重新生成SVG，使用纯矢量方法\n"
        report += "- 避免嵌入位图图像\n"
        report += "- 增加矢量元素数量\n"
    else:
        report += "- SVG质量良好，可在专业软件中编辑\n"
        report += "- 建议在Adobe Illustrator或Inkscape中测试\n"
    
    # 保存报告
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"质量报告已保存: {output_file}")
    
    return report

def main():
    """主函数 - 演示工具使用"""
    print("SVG质量验证工具包")
    print("="*30)
    
    # 验证我们生成的可编辑SVG
    svg_file = "03_图表输出/论文级图表/分子骨架前10个_可编辑版.svg"
    
    if os.path.exists(svg_file):
        # 单文件验证
        validator = SVGQualityValidator()
        is_valid, details = validator.validate_file(svg_file)
        
        # 生成详细报告
        report_file = svg_file.replace('.svg', '_质量报告.txt')
        generate_quality_report(svg_file, report_file)
        
        print(f"\n🎯 验证结果: {'通过' if is_valid else '失败'}")
        
    else:
        print(f"文件不存在: {svg_file}")
        print("请先生成可编辑SVG文件")

if __name__ == "__main__":
    main()
