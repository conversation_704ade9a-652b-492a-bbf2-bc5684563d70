# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-11
# 描述: 化学数据集R可视化脚本 - Nature期刊标准

# 加载必要的包
suppressMessages({
    library(ggplot2)
    library(dplyr)
    library(readr)
    library(gridExtra)
    library(scales)
})

# 设置随机种子
set.seed(42)

# Nature风格主题
theme_nature <- function(base_size = 7) {
    theme_bw(base_size = base_size) +
        theme(
            panel.grid.major = element_blank(),
            panel.grid.minor = element_blank(),
            panel.border = element_rect(colour = "black", fill = NA, size = 0.5),
            plot.title = element_text(hjust = 0.5, size = rel(1.1)),
            axis.text = element_text(color = "black"),
            axis.ticks = element_line(colour = "black", size = 0.5),
            legend.key = element_blank(),
            strip.background = element_rect(fill = "white", colour = "black")
        )
}

# Wong配色方案 (色盲友好)
wong_colors <- c("#000000", "#E69F00", "#56B4E9", "#009E73",
                 "#F0E442", "#0072B2", "#D55E00", "#CC79A7")

# 数据加载和预处理函数
load_and_process_data <- function() {
    cat("正在加载数据集...\n")
    
    # 加载数据集1
    tryCatch({
        data1 <- read_csv("cleaned_data-12-9-no-duplicate.csv", 
                         show_col_types = FALSE)
        cat("数据集1加载成功:", nrow(data1), "行\n")
    }, error = function(e) {
        cat("数据集1加载失败:", e$message, "\n")
        return(NULL)
    })
    
    # 加载数据集2
    tryCatch({
        data2 <- read_csv("filtered_ecoli_9000-nosalt.csv", 
                         show_col_types = FALSE)
        cat("数据集2加载成功:", nrow(data2), "行\n")
    }, error = function(e) {
        cat("数据集2加载失败:", e$message, "\n")
        return(NULL)
    })
    
    # 数据预处理
    if (exists("data1") && exists("data2")) {
        # 添加数据集标识
        data1$Dataset <- "Dataset 1 (Cleaned)"
        data2$Dataset <- "Dataset 2 (E.coli)"
        
        # 计算SMILES长度
        data1$SMILES_Length <- nchar(data1$Smiles)
        data2$SMILES_Length <- nchar(data2$Smiles)
        
        # 转换Activity为因子
        data1$Activity <- factor(data1$Activity, levels = c(0, 1), 
                                labels = c("Inactive", "Active"))
        data2$Activity <- factor(data2$Activity, levels = c(0, 1), 
                                labels = c("Inactive", "Active"))
        
        return(list(data1 = data1, data2 = data2))
    } else {
        return(NULL)
    }
}

# 创建数据集概览图
create_overview_plot <- function(data_list) {
    cat("创建数据集概览图...\n")
    
    # 数据集规模对比
    dataset_sizes <- data.frame(
        Dataset = c("Dataset 1\n(Cleaned)", "Dataset 2\n(E.coli)"),
        Size = c(nrow(data_list$data1), nrow(data_list$data2))
    )
    
    p1 <- ggplot(dataset_sizes, aes(x = Dataset, y = Size, fill = Dataset)) +
        geom_bar(stat = "identity", color = "black", size = 0.5) +
        scale_fill_manual(values = wong_colors[1:2]) +
        geom_text(aes(label = comma(Size)), vjust = -0.5, size = 2.5) +
        labs(title = "Dataset Sizes", 
             x = "", y = "Number of Records") +
        theme_nature() +
        theme(legend.position = "none") +
        scale_y_continuous(labels = comma, expand = expansion(mult = c(0, 0.1)))
    
    return(p1)
}

# 创建SMILES长度分布图
create_smiles_length_plot <- function(data_list) {
    cat("创建SMILES长度分布图...\n")
    
    # 合并数据
    combined_data <- rbind(
        data.frame(Length = data_list$data1$SMILES_Length, 
                  Dataset = "Dataset 1"),
        data.frame(Length = data_list$data2$SMILES_Length, 
                  Dataset = "Dataset 2")
    )
    
    p2 <- ggplot(combined_data, aes(x = Length, fill = Dataset)) +
        geom_histogram(bins = 30, alpha = 0.7, color = "black", size = 0.3) +
        scale_fill_manual(values = wong_colors[1:2]) +
        labs(title = "SMILES Length Distribution",
             x = "SMILES Length (characters)", y = "Frequency") +
        theme_nature() +
        facet_wrap(~Dataset, scales = "free_y") +
        theme(legend.position = "none")
    
    return(p2)
}

# 创建活性分布图
create_activity_plot <- function(data_list) {
    cat("创建活性分布图...\n")
    
    # 计算活性分布
    activity1 <- table(data_list$data1$Activity)
    activity2 <- table(data_list$data2$Activity)
    
    # 创建数据框
    activity_data <- data.frame(
        Dataset = rep(c("Dataset 1", "Dataset 2"), each = 2),
        Activity = rep(c("Inactive", "Active"), 2),
        Count = c(activity1[1], activity1[2], activity2[1], activity2[2]),
        Percentage = c(activity1[1]/sum(activity1)*100, activity1[2]/sum(activity1)*100,
                      activity2[1]/sum(activity2)*100, activity2[2]/sum(activity2)*100)
    )
    
    p3 <- ggplot(activity_data, aes(x = Activity, y = Count, fill = Activity)) +
        geom_bar(stat = "identity", color = "black", size = 0.5) +
        scale_fill_manual(values = wong_colors[3:4]) +
        geom_text(aes(label = paste0(round(Percentage, 1), "%")), 
                 vjust = -0.5, size = 2.5) +
        labs(title = "Activity Distribution",
             x = "Activity Class", y = "Number of Compounds") +
        theme_nature() +
        facet_wrap(~Dataset, scales = "free_y") +
        theme(legend.position = "none") +
        scale_y_continuous(labels = comma, expand = expansion(mult = c(0, 0.1)))
    
    return(p3)
}

# 创建SMILES复杂性分析图
create_complexity_plot <- function(data_list) {
    cat("创建SMILES复杂性分析图...\n")
    
    # 合并数据用于箱线图
    combined_data <- rbind(
        data.frame(Length = data_list$data1$SMILES_Length,
                  Activity = data_list$data1$Activity,
                  Dataset = "Dataset 1"),
        data.frame(Length = data_list$data2$SMILES_Length,
                  Activity = data_list$data2$Activity,
                  Dataset = "Dataset 2")
    )
    
    p4 <- ggplot(combined_data, aes(x = Activity, y = Length, fill = Activity)) +
        geom_boxplot(color = "black", size = 0.5, alpha = 0.7) +
        scale_fill_manual(values = wong_colors[3:4]) +
        labs(title = "SMILES Complexity by Activity",
             x = "Activity Class", y = "SMILES Length (characters)") +
        theme_nature() +
        facet_wrap(~Dataset) +
        theme(legend.position = "none")
    
    return(p4)
}

# 主函数
main <- function() {
    cat("开始创建R可视化图表\n")
    cat("使用Nature期刊标准和Wong配色方案\n")
    cat(rep("=", 50), "\n")
    
    # 加载数据
    data_list <- load_and_process_data()
    if (is.null(data_list)) {
        cat("数据加载失败，无法创建图表\n")
        return()
    }
    
    # 创建图表
    p1 <- create_overview_plot(data_list)
    p2 <- create_smiles_length_plot(data_list)
    p3 <- create_activity_plot(data_list)
    p4 <- create_complexity_plot(data_list)
    
    # 保存单独图表
    cat("保存图表文件...\n")
    
    # 图1: 数据集概览
    ggsave("Figure1_Dataset_Overview.pdf", p1, 
           width = 89, height = 60, units = "mm", device = "pdf")
    ggsave("Figure1_Dataset_Overview.png", p1, 
           width = 89, height = 60, units = "mm", dpi = 300)
    
    # 图2: SMILES长度分布
    ggsave("Figure2_SMILES_Length.pdf", p2, 
           width = 183, height = 80, units = "mm", device = "pdf")
    ggsave("Figure2_SMILES_Length.png", p2, 
           width = 183, height = 80, units = "mm", dpi = 300)
    
    # 图3: 活性分布
    ggsave("Figure3_Activity_Distribution.pdf", p3, 
           width = 183, height = 80, units = "mm", device = "pdf")
    ggsave("Figure3_Activity_Distribution.png", p3, 
           width = 183, height = 80, units = "mm", dpi = 300)
    
    # 图4: 复杂性分析
    ggsave("Figure4_Complexity_Analysis.pdf", p4, 
           width = 183, height = 80, units = "mm", device = "pdf")
    ggsave("Figure4_Complexity_Analysis.png", p4, 
           width = 183, height = 80, units = "mm", dpi = 300)
    
    # 创建组合图
    cat("创建组合图表...\n")
    combined_plot <- grid.arrange(p1, p2, p3, p4, 
                                 ncol = 2, nrow = 2,
                                 top = "Chemical Datasets Quality Analysis")
    
    # 保存组合图
    ggsave("Combined_Analysis_Plots.pdf", combined_plot, 
           width = 183, height = 120, units = "mm", device = "pdf")
    ggsave("Combined_Analysis_Plots.png", combined_plot, 
           width = 183, height = 120, units = "mm", dpi = 300)
    
    cat("\n图表创建完成！\n")
    cat("生成的文件:\n")
    cat("- Figure1_Dataset_Overview.pdf/.png\n")
    cat("- Figure2_SMILES_Length.pdf/.png\n")
    cat("- Figure3_Activity_Distribution.pdf/.png\n")
    cat("- Figure4_Complexity_Analysis.pdf/.png\n")
    cat("- Combined_Analysis_Plots.pdf/.png\n")
    
    # 显示数据统计摘要
    cat("\n数据统计摘要:\n")
    cat("数据集1: ", nrow(data_list$data1), " 个化合物\n")
    cat("数据集2: ", nrow(data_list$data2), " 个化合物\n")
    cat("SMILES长度范围 (数据集1): ", 
        min(data_list$data1$SMILES_Length), "-", 
        max(data_list$data1$SMILES_Length), " 字符\n")
    cat("SMILES长度范围 (数据集2): ", 
        min(data_list$data2$SMILES_Length), "-", 
        max(data_list$data2$SMILES_Length), " 字符\n")
}

# 执行主函数
main()
