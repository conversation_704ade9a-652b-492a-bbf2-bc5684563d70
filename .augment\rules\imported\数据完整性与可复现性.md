---
type: "agent_requested"
---

name: 数据完整性与可复现性
description: 设定关于数据使用的黄金准则和保障分析可复现性的强制要求，这是所有科学分析的基石。
---
# 数据完整性与可复现性规范

## 1. 数据完整性：黄金准则

**第一条，也是最重要的一条规则：严禁以任何形式伪造、编造、或篡改任何数据。**

- **一切皆有源**: 所有的分析、图表、表格和结论，都必须**直接、唯一地**源于 [模式数据](mdc:模式数据) 目录下用户提供的原始数据。
- **禁止凭空创造**: 不得在没有真实数据支持的情况下，生成任何看似合理的结果。如果数据缺失或不足以支撑某个结论，必须如实报告这一局限性，而不是试图填补它。
- **明确数据溯源**: 在报告的 **"数据与方法"** 部分，必须像引用文献一样，清晰、准确地注明分析所基于的具体数据文件名。这是确保分析透明性的关键。

## 2. 可复现性：科学的基石

一个无法被重复的分析是没有说服力的。因此，所有分析脚本必须满足以下可复现性要求：

- **代码清晰可读**:
    - R脚本应编写得逻辑清晰、结构合理。
    - 对复杂的代码块、非标准的处理方法或关键参数选择，必须添加充分的注释进行解释。
- **随机过程的固定**:
    - 对于任何涉及随机性的过程（例如，随机抽样、交叉验证分组、机器学习模型初始化等），**必须使用 `set.seed()` 函数**来设置一个固定的随机种子。
    - 这确保了任何人使用相同的代码和数据，都能得到完全相同的结果。
    - `set.seed()` 应在执行随机操作前立即调用。
    ```R
    # 示例
    set.seed(42) # 使用一个经典的种子
    train_indices <- sample(1:nrow(data), 0.8 * nrow(data))
    train_data <- data[train_indices, ]
    ```
- **环境信息记录**:
    - 在报告的末尾或附录中，**强烈建议**使用 `sessionInfo()` 或 `devtools::session_info()` 函数输出当前的R会话信息。
    - 这包括R的版本号、操作系统、以及所有加载的包（package）及其版本号。这对于他人在未来复现你的工作至关重要。
- **路径的相对性**:
    - 在代码中引用文件路径时，应始终使用相对路径（相对于项目根目录），而不是绝对路径（如 "C:/Users/<USER>"）。这保证了项目在任何计算机上都能直接运行。

**违反这些规则将从根本上损害分析的科学价值。AI在执行任务时，必须将这些规范作为最高优先级。**
