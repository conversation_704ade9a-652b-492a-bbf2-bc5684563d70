---
type: "agent_requested"
---

name: 学术报告撰写规范
description: 定义数据分析报告的结构、内容和写作风格，使其符合国际学术期刊的发表标准。
---
# 学术数据分析报告撰写规范

## 1. 核心原则
报告的唯一目的是**清晰、准确、高效地传达研究成果**。每一部分都应服务于此目的。写作风格应**客观、精炼、专业**，避免任何主观臆断和口语化表达。

## 2. 标准报告结构 (IMRaD 变体)
所有分析报告必须严格遵循以下四段式结构：

**I. 引言 (Introduction / Overview)**
- **研究背景**: 简要介绍相关领域的研究现状。
- **核心问题**: 清晰地陈述本分析旨在解决或探讨的具体科学问题。
- **分析目标**: 明确说明本次数据分析的目的和预期贡献。
- *篇幅建议: 1-2段。*

**II. 数据与方法 (Data and Methods)**
- **数据来源**:
    - 详细描述所用数据集的来源（例如，实验、公开数据库、文献等）。
    - **必须**明确指出具体使用了哪个或哪些数据文件。例如: "本研究的数据来源于 [模式数据/cleaned_data-12-9-no-duplicate.csv](mdc:模式数据/cleaned_data-12-9-no-duplicate.csv) 文件。"
- **数据特征**: 描述数据集的关键特征，如样本量、变量数、重要变量的定义等。
- **预处理步骤**: 清晰、完整地列出所有数据清洗和预处理的步骤。
- **分析方法**:
    - 详细说明所采用的统计分析方法或机器学习模型（例如，“使用R语言`randomForest`包实现随机森林模型”）。
    - 简要阐述选择该特定方法的原因。
    - **必须**提及所使用的软件和关键的包/库（例如，"数据分析基于R (v4.3.1) 和 ggplot2 (v3.4.0) 完成"）。

**III. 结果 (Results)**
- **客观呈现**: 本部分只用于客观展示分析得到的事实和数据，**不包含任何主观解释或讨论**。
- **图文并茂**:
    - 以图表为核心来展示结果，辅以简要的文字说明。
    - 所有图表必须按顺序编号（如 Figure 1, Figure 2; Table 1, Table 2）。
    - 每个图表下方必须配有信息完整的**标题 (Caption)**。标题应能让读者在不读正文的情况下理解图表内容。
- **正文引用**: 在正文中必须引用所有图表，并引导读者观察关键信息。例如："结果显示，A组和B组之间存在显著差异（Figure 1, p < 0.05）。"
- **关键数据**: 将最重要的统计结果（如p值、置信区间、效应量等）直接在文本中报告。

**IV. 结论与讨论 (Conclusion / Discussion)**
- **结果总结**: 首先，用1-2句话概括最重要的发现。
- **问题回答**: 回答在“引言”部分提出的核心问题。
- **结果解读**: 对“结果”部分展示的数据进行深入的科学解释，并将其与现有研究联系起来。
- **局限性**: （可选但推荐）诚实地指出本研究的局限之处。
- **未来展望**: 提出未来可能的研究方向。

## 3. 写作禁忌
- **禁止口语化叙事**: 严禁使用 "我首先尝试了A，但失败了，然后我尝试了B..." 这样的流水账句式。报告应展现的是最终的、逻辑清晰的分析路径。
- **禁止包含代码**: 最终报告中**不应出现任何计算机代码块**。代码应作为附录或单独文件提供。
- **避免使用第一人称**: 尽量使用被动语态或第三人称（例如，"本研究发现..." 而不是 "我发现..."）。
