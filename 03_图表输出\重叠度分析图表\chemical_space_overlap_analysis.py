# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-11
# 描述: 化学空间重叠度分析 - 天然产物库 vs 训练数据集

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
np.random.seed(42)

# 设置matplotlib参数
plt.rcParams['font.size'] = 8
plt.rcParams['axes.linewidth'] = 0.5
plt.rcParams['figure.dpi'] = 300

# Wong配色方案
wong_colors = ['#000000', '#E69F00', '#56B4E9', '#009E73', 
               '#F0E442', '#0072B2', '#D55E00', '#CC79A7']

def load_datasets():
    """加载所有数据集"""
    print("正在加载数据集...")
    
    # 加载训练数据集
    train1 = pd.read_csv("cleaned_data-12-9-no-duplicate.csv")
    train1['Dataset'] = 'S. aureus Training'
    train1['Type'] = 'Training'
    
    train2 = pd.read_csv("filtered_ecoli_9000-nosalt.csv")
    train2['Dataset'] = 'E. coli Training'
    train2['Type'] = 'Training'
    
    # 统一列名
    train1_clean = train1[['Smiles', 'Activity', 'Dataset', 'Type']].rename(columns={'Smiles': 'SMILES'})
    train2_clean = train2[['Smiles', 'Activity', 'Dataset', 'Type']].rename(columns={'Smiles': 'SMILES'})
    
    # 加载天然产物数据
    try:
        coconut = pd.read_csv("天然产物/clean-coconut_filtered_with_weight_under_500-10-2024.csv")
        coconut = coconut.rename(columns={'canonical_smiles': 'SMILES'})
        coconut['Dataset'] = 'COCONUT Natural Products'
        coconut['Type'] = 'Natural Products'
        coconut['Activity'] = -1  # 标记为未知活性
        print(f"COCONUT数据加载成功: {len(coconut)}个化合物")
    except:
        print("COCONUT数据加载失败")
        coconut = pd.DataFrame()
    
    try:
        zinc = pd.read_csv("天然产物/merged_zinc_data.csv")
        zinc = zinc.rename(columns={'smiles': 'SMILES'})
        zinc['Dataset'] = 'ZINC Natural Products'
        zinc['Type'] = 'Natural Products'
        zinc['Activity'] = -1  # 标记为未知活性
        print(f"ZINC数据加载成功: {len(zinc)}个化合物")
    except:
        print("ZINC数据加载失败")
        zinc = pd.DataFrame()
    
    print(f"训练数据集1: {len(train1_clean)}个化合物")
    print(f"训练数据集2: {len(train2_clean)}个化合物")
    
    return train1_clean, train2_clean, coconut, zinc

def calculate_molecular_features(smiles_series):
    """计算分子特征"""
    print("计算分子特征...")
    
    features = pd.DataFrame({
        'SMILES_Length': smiles_series.str.len(),
        'Carbon_Count': smiles_series.str.count('C'),
        'Nitrogen_Count': smiles_series.str.count('N'),
        'Oxygen_Count': smiles_series.str.count('O'),
        'Sulfur_Count': smiles_series.str.count('S'),
        'Fluorine_Count': smiles_series.str.count('F'),
        'Chlorine_Count': smiles_series.str.count('Cl'),
        'Ring_Count': smiles_series.str.count(r'[0-9]'),
        'Aromatic_Count': smiles_series.str.count(r'[cnos]'),
        'Double_Bond_Count': smiles_series.str.count('='),
        'Stereochemistry_Count': smiles_series.str.count('@'),
        'Branch_Count': smiles_series.str.count(r'\('),
        # 复杂性指标
        'Heteroatom_Ratio': (smiles_series.str.count('N') + 
                            smiles_series.str.count('O') + 
                            smiles_series.str.count('S')) / smiles_series.str.len(),
        'Complexity_Score': (smiles_series.str.count(r'[0-9]') + 
                            smiles_series.str.count('@') + 
                            smiles_series.str.count(r'\(')) / smiles_series.str.len()
    })
    
    # 处理NaN和无穷值
    features = features.fillna(0)
    features = features.replace([np.inf, -np.inf], 0)
    
    return features

def stratified_sampling(data, sample_ratio=0.05, stratify_cols=['SMILES_Length']):
    """分层抽样"""
    print(f"进行分层抽样，抽样比例: {sample_ratio}")
    
    if len(data) == 0:
        return data
    
    # 计算分层变量
    if 'SMILES_Length' in stratify_cols:
        data['Length_Bin'] = pd.cut(data['SMILES_Length'], 
                                   bins=[0, 50, 100, 200, 500, np.inf], 
                                   labels=['Very_Short', 'Short', 'Medium', 'Long', 'Very_Long'])
    
    # 分层抽样
    sampled_data = []
    for group_name, group_data in data.groupby('Length_Bin'):
        n_samples = max(1, int(len(group_data) * sample_ratio))
        if len(group_data) >= n_samples:
            sampled_group = group_data.sample(n=n_samples, random_state=42)
        else:
            sampled_group = group_data
        sampled_data.append(sampled_group)
    
    result = pd.concat(sampled_data, ignore_index=True)
    print(f"抽样结果: {len(result)}个化合物 (原始: {len(data)})")
    
    return result

def perform_chemical_space_analysis(combined_data, features):
    """执行化学空间分析"""
    print("执行化学空间PCA分析...")
    
    # 标准化特征
    scaler = StandardScaler()
    features_scaled = scaler.fit_transform(features)
    
    # PCA降维
    pca = PCA(n_components=2)
    pca_coords = pca.fit_transform(features_scaled)
    
    # 计算解释方差
    var_explained = pca.explained_variance_ratio_ * 100
    print(f"PC1解释方差: {var_explained[0]:.2f}%")
    print(f"PC2解释方差: {var_explained[1]:.2f}%")
    print(f"总解释方差: {var_explained.sum():.2f}%")
    
    # 添加PCA坐标到数据
    combined_data['PC1'] = pca_coords[:, 0]
    combined_data['PC2'] = pca_coords[:, 1]
    
    return combined_data, var_explained

def calculate_overlap_metrics(train_data, np_data):
    """计算重叠度指标"""
    print("计算化学空间重叠度指标...")
    
    # 简化的重叠度计算（基于PCA空间）
    train_pc1_range = [train_data['PC1'].min(), train_data['PC1'].max()]
    train_pc2_range = [train_data['PC2'].min(), train_data['PC2'].max()]
    
    # 计算天然产物在训练数据空间范围内的比例
    np_in_train_space = np_data[
        (np_data['PC1'] >= train_pc1_range[0]) & 
        (np_data['PC1'] <= train_pc1_range[1]) &
        (np_data['PC2'] >= train_pc2_range[0]) & 
        (np_data['PC2'] <= train_pc2_range[1])
    ]
    
    overlap_ratio = len(np_in_train_space) / len(np_data) * 100
    novelty_ratio = 100 - overlap_ratio
    
    print(f"化学空间重叠度: {overlap_ratio:.2f}%")
    print(f"新颖性比例: {novelty_ratio:.2f}%")
    
    return {
        'overlap_ratio': overlap_ratio,
        'novelty_ratio': novelty_ratio,
        'overlapping_compounds': len(np_in_train_space),
        'total_np_compounds': len(np_data)
    }

def create_overlap_visualizations(data, var_explained, overlap_metrics):
    """创建重叠度可视化图表 - 基于Nature期刊标准"""
    print("创建化学空间重叠度可视化...")

    # 创建图表 - 使用Nature期刊标准尺寸
    fig, axes = plt.subplots(2, 2, figsize=(183/25.4, 120/25.4))  # Nature双栏宽度
    fig.suptitle('Chemical Space Overlap Analysis: Natural Products vs Training Data',
                 fontsize=10, y=0.95)
    
    # 轴标签
    pc1_label = f"PC1 ({var_explained[0]:.1f}% variance)"
    pc2_label = f"PC2 ({var_explained[1]:.1f}% variance)"
    
    # 图1: 整体化学空间分布
    for i, (dataset, color) in enumerate(zip(['Training', 'Natural Products'], wong_colors[:2])):
        subset = data[data['Type'] == dataset]
        axes[0,0].scatter(subset['PC1'], subset['PC2'], 
                         c=color, alpha=0.6, s=8, label=dataset)
    
    axes[0,0].set_xlabel(pc1_label)
    axes[0,0].set_ylabel(pc2_label)
    axes[0,0].set_title('Overall Chemical Space Distribution')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # 图2: 按数据集详细分类
    datasets = data['Dataset'].unique()
    colors = wong_colors[:len(datasets)]
    
    for dataset, color in zip(datasets, colors):
        subset = data[data['Dataset'] == dataset]
        axes[0,1].scatter(subset['PC1'], subset['PC2'], 
                         c=color, alpha=0.6, s=8, label=dataset)
    
    axes[0,1].set_xlabel(pc1_label)
    axes[0,1].set_ylabel(pc2_label)
    axes[0,1].set_title('Chemical Space by Dataset')
    axes[0,1].legend(fontsize=8)
    axes[0,1].grid(True, alpha=0.3)
    
    # 图3: 分子复杂性分布
    training_data = data[data['Type'] == 'Training']
    np_data = data[data['Type'] == 'Natural Products']
    
    axes[1,0].hist(training_data['SMILES_Length'], bins=30, alpha=0.7, 
                   color=wong_colors[0], label='Training Data', density=True)
    axes[1,0].hist(np_data['SMILES_Length'], bins=30, alpha=0.7, 
                   color=wong_colors[1], label='Natural Products', density=True)
    axes[1,0].set_xlabel('SMILES Length')
    axes[1,0].set_ylabel('Density')
    axes[1,0].set_title('Molecular Complexity Distribution')
    axes[1,0].legend()
    axes[1,0].grid(True, alpha=0.3)
    
    # 图4: 重叠度统计
    categories = ['Overlapping', 'Novel']
    values = [overlap_metrics['overlap_ratio'], overlap_metrics['novelty_ratio']]
    
    bars = axes[1,1].bar(categories, values, color=wong_colors[2:4])
    axes[1,1].set_ylabel('Percentage (%)')
    axes[1,1].set_title('Chemical Space Overlap Statistics')
    axes[1,1].grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        axes[1,1].text(bar.get_x() + bar.get_width()/2., height + 1,
                      f'{value:.1f}%', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('Chemical_Space_Overlap_Analysis.pdf', dpi=300, bbox_inches='tight')
    plt.savefig('Chemical_Space_Overlap_Analysis.png', dpi=300, bbox_inches='tight')
    print("图表已保存: Chemical_Space_Overlap_Analysis.pdf/.png")
    
    plt.show()

def main():
    """主分析函数"""
    print("开始化学空间重叠度分析")
    print("="*60)
    
    # 加载数据
    train1, train2, coconut, zinc = load_datasets()
    
    # 合并训练数据
    training_data = pd.concat([train1, train2], ignore_index=True)
    print(f"合并训练数据: {len(training_data)}个化合物")
    
    # 合并天然产物数据并抽样
    natural_products = pd.concat([coconut, zinc], ignore_index=True)
    if len(natural_products) > 0:
        # 对天然产物进行抽样以提高计算效率
        natural_products_sampled = stratified_sampling(natural_products, sample_ratio=0.03)
    else:
        natural_products_sampled = natural_products
    
    # 合并所有数据
    all_data = pd.concat([training_data, natural_products_sampled], ignore_index=True)
    all_data = all_data.dropna(subset=['SMILES'])
    all_data = all_data[all_data['SMILES'].str.len() > 5]  # 过滤过短的SMILES
    
    print(f"最终分析数据: {len(all_data)}个化合物")
    
    # 计算分子特征
    features = calculate_molecular_features(all_data['SMILES'])
    all_data = pd.concat([all_data.reset_index(drop=True), features], axis=1)
    
    # 执行化学空间分析
    analyzed_data, var_explained = perform_chemical_space_analysis(all_data, features)
    
    # 计算重叠度指标
    train_subset = analyzed_data[analyzed_data['Type'] == 'Training']
    np_subset = analyzed_data[analyzed_data['Type'] == 'Natural Products']
    
    if len(np_subset) > 0:
        overlap_metrics = calculate_overlap_metrics(train_subset, np_subset)
        
        # 创建可视化
        create_overlap_visualizations(analyzed_data, var_explained, overlap_metrics)
        
        # 保存结果
        overlap_summary = pd.DataFrame([overlap_metrics])
        overlap_summary.to_csv('Chemical_Space_Overlap_Metrics.csv', index=False)
        print("重叠度指标已保存: Chemical_Space_Overlap_Metrics.csv")
        
        # 输出总结
        print("\n" + "="*60)
        print("化学空间重叠度分析完成")
        print("="*60)
        print(f"训练数据集化合物: {len(train_subset)}")
        print(f"天然产物化合物: {len(np_subset)}")
        print(f"化学空间重叠度: {overlap_metrics['overlap_ratio']:.2f}%")
        print(f"新颖化合物比例: {overlap_metrics['novelty_ratio']:.2f}%")
        print(f"PCA解释方差: {var_explained.sum():.2f}%")
    else:
        print("天然产物数据为空，无法进行重叠度分析")

if __name__ == "__main__":
    main()
