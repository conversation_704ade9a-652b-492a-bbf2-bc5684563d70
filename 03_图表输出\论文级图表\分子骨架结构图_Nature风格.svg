<?xml version='1.0' encoding='UTF-8' ?>
<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='864.00pt' height='720.00pt' viewBox='0 0 864.00 720.00'>
<g class='svglite'>
<defs>
  <style type='text/css'><![CDATA[
    .svglite line, .svglite polyline, .svglite polygon, .svglite path, .svglite rect, .svglite circle {
      fill: none;
      stroke: #000000;
      stroke-linecap: round;
      stroke-linejoin: round;
      stroke-miterlimit: 10.00;
    }
    .svglite text {
      white-space: pre;
    }
    .svglite g.glyphgroup path {
      fill: inherit;
      stroke: none;
    }
  ]]></style>
</defs>
<rect width='100%' height='100%' style='stroke: none; fill: #FFFFFF;'/>
<defs>
  <clipPath id='cpMC4wMHw4NjQuMDB8MC4wMHw3MjAuMDA='>
    <rect x='0.00' y='0.00' width='864.00' height='720.00' />
  </clipPath>
</defs>
<g clip-path='url(#cpMC4wMHw4NjQuMDB8MC4wMHw3MjAuMDA=)'>
</g>
<defs>
  <clipPath id='cpMjExLjI2fDY1Mi43NHwwLjAwfDcyMC4wMA=='>
    <rect x='211.26' y='0.00' width='441.48' height='720.00' />
  </clipPath>
</defs>
<g clip-path='url(#cpMjExLjI2fDY1Mi43NHwwLjAwfDcyMC4wMA==)'>
<rect x='211.26' y='0.00' width='441.48' height='720.00' style='stroke-width: 0.97; stroke: none; fill: #FDFEFE;' />
</g>
<g clip-path='url(#cpMC4wMHw4NjQuMDB8MC4wMHw3MjAuMDA=)'>
</g>
<defs>
  <clipPath id='cpMjU5LjgzfDY0Ny43Nnw2My45NXw1OTcuMzU='>
    <rect x='259.83' y='63.95' width='387.93' height='533.40' />
  </clipPath>
</defs>
<g clip-path='url(#cpMjU5LjgzfDY0Ny43Nnw2My45NXw1OTcuMzU=)'>
<rect x='259.83' y='63.95' width='387.93' height='533.40' style='stroke-width: 0.97; stroke: none; fill: #FDFEFE;' />
<circle cx='321.55' cy='132.28' r='7.47' style='stroke-width: 1.70; stroke: #355876; stroke-opacity: 0.80; fill: #355876; fill-opacity: 0.80;' />
<circle cx='409.71' cy='132.28' r='6.94' style='stroke-width: 1.70; stroke: #355877; stroke-opacity: 0.80; fill: #355877; fill-opacity: 0.80;' />
<circle cx='497.88' cy='132.28' r='4.05' style='stroke-width: 1.70; stroke: #2980B9; stroke-opacity: 0.80; fill: #2980B9; fill-opacity: 0.80;' />
<circle cx='586.04' cy='132.28' r='7.93' style='stroke-width: 1.70; stroke: #344C63; stroke-opacity: 0.80; fill: #344C63; fill-opacity: 0.80;' />
<circle cx='321.55' cy='220.44' r='8.72' style='stroke-width: 1.70; stroke: #844745; stroke-opacity: 0.80; fill: #844745; fill-opacity: 0.80;' />
<circle cx='409.71' cy='220.44' r='6.29' style='stroke-width: 1.70; stroke: #34678F; stroke-opacity: 0.80; fill: #34678F; fill-opacity: 0.80;' />
<circle cx='497.88' cy='220.44' r='6.29' style='stroke-width: 1.70; stroke: #344C63; stroke-opacity: 0.80; fill: #344C63; fill-opacity: 0.80;' />
<circle cx='586.04' cy='220.44' r='9.06' style='stroke-width: 1.70; stroke: #8A4643; stroke-opacity: 0.80; fill: #8A4643; fill-opacity: 0.80;' />
<circle cx='321.55' cy='308.61' r='7.93' style='stroke-width: 1.70; stroke: #344C63; stroke-opacity: 0.80; fill: #344C63; fill-opacity: 0.80;' />
<circle cx='409.71' cy='308.61' r='7.47' style='stroke-width: 1.70; stroke: #35516B; stroke-opacity: 0.80; fill: #35516B; fill-opacity: 0.80;' />
<circle cx='497.88' cy='308.61' r='8.34' style='stroke-width: 1.70; stroke: #5A4954; stroke-opacity: 0.80; fill: #5A4954; fill-opacity: 0.80;' />
<circle cx='586.04' cy='308.61' r='7.71' style='stroke-width: 1.70; stroke: #354F68; stroke-opacity: 0.80; fill: #354F68; fill-opacity: 0.80;' />
<circle cx='321.55' cy='396.77' r='9.06' style='stroke-width: 1.70; stroke: #A0423A; stroke-opacity: 0.80; fill: #A0423A; fill-opacity: 0.80;' />
<circle cx='409.71' cy='396.77' r='9.23' style='stroke-width: 1.70; stroke: #A74137; stroke-opacity: 0.80; fill: #A74137; fill-opacity: 0.80;' />
<circle cx='497.88' cy='396.77' r='8.53' style='stroke-width: 1.70; stroke: #AD3F34; stroke-opacity: 0.80; fill: #AD3F34; fill-opacity: 0.80;' />
<circle cx='586.04' cy='396.77' r='8.72' style='stroke-width: 1.70; stroke: #8A4643; stroke-opacity: 0.80; fill: #8A4643; fill-opacity: 0.80;' />
<circle cx='321.55' cy='484.94' r='8.14' style='stroke-width: 1.70; stroke: #6A494F; stroke-opacity: 0.80; fill: #6A494F; fill-opacity: 0.80;' />
<circle cx='409.71' cy='484.94' r='8.53' style='stroke-width: 1.70; stroke: #814746; stroke-opacity: 0.80; fill: #814746; fill-opacity: 0.80;' />
<circle cx='497.88' cy='484.94' r='9.39' style='stroke-width: 1.70; stroke: #C0392B; stroke-opacity: 0.80; fill: #C0392B; fill-opacity: 0.80;' />
<circle cx='586.04' cy='484.94' r='9.23' style='stroke-width: 1.70; stroke: #B13E32; stroke-opacity: 0.80; fill: #B13E32; fill-opacity: 0.80;' />
<text x='321.55' y='124.64' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='35.95px' lengthAdjust='spacingAndGlyphs'>Scaffold_1</text>
<text x='409.71' y='124.64' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='35.95px' lengthAdjust='spacingAndGlyphs'>Scaffold_2</text>
<text x='497.88' y='124.64' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='35.95px' lengthAdjust='spacingAndGlyphs'>Scaffold_3</text>
<text x='586.04' y='124.64' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='35.95px' lengthAdjust='spacingAndGlyphs'>Scaffold_4</text>
<text x='321.55' y='212.81' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='35.95px' lengthAdjust='spacingAndGlyphs'>Scaffold_5</text>
<text x='409.71' y='212.81' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_12</text>
<text x='497.88' y='212.81' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_13</text>
<text x='586.04' y='212.81' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_14</text>
<text x='321.55' y='300.97' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_15</text>
<text x='409.71' y='300.97' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_22</text>
<text x='497.88' y='300.97' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_27</text>
<text x='586.04' y='300.97' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_29</text>
<text x='321.55' y='389.14' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_35</text>
<text x='409.71' y='389.14' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_36</text>
<text x='497.88' y='389.14' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_38</text>
<text x='586.04' y='389.14' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_39</text>
<text x='321.55' y='477.30' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_43</text>
<text x='409.71' y='477.30' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_44</text>
<text x='497.88' y='477.30' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_45</text>
<text x='586.04' y='477.30' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='39.91px' lengthAdjust='spacingAndGlyphs'>Scaffold_47</text>
<text x='321.55' y='141.42' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:13 N:0 O:2</text>
<text x='409.71' y='141.42' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.45px' lengthAdjust='spacingAndGlyphs'>C:11 N:0 O:3</text>
<text x='497.88' y='141.42' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='26.98px' lengthAdjust='spacingAndGlyphs'>C:6 N:0 O:0</text>
<text x='586.04' y='141.42' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:15 N:0 O:2</text>
<text x='321.55' y='229.59' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:19 N:1 O:2</text>
<text x='409.71' y='229.59' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='26.98px' lengthAdjust='spacingAndGlyphs'>C:9 N:0 O:2</text>
<text x='497.88' y='229.59' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='26.98px' lengthAdjust='spacingAndGlyphs'>C:9 N:3 O:2</text>
<text x='586.04' y='229.59' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:21 N:1 O:3</text>
<text x='321.55' y='317.75' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:15 N:0 O:2</text>
<text x='409.71' y='317.75' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:13 N:1 O:1</text>
<text x='497.88' y='317.75' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:17 N:0 O:3</text>
<text x='586.04' y='317.75' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:14 N:0 O:2</text>
<text x='321.55' y='405.92' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:21 N:2 O:3</text>
<text x='409.71' y='405.92' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:22 N:2 O:3</text>
<text x='497.88' y='405.92' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:18 N:3 O:2</text>
<text x='586.04' y='405.92' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:19 N:1 O:3</text>
<text x='321.55' y='494.08' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:16 N:1 O:2</text>
<text x='409.71' y='494.08' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:18 N:1 O:2</text>
<text x='497.88' y='494.08' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:23 N:2 O:4</text>
<text x='586.04' y='494.08' text-anchor='middle' style='font-size: 5.12px;fill: #34495E; fill-opacity: 0.70; font-family: "Arial";' textLength='29.83px' lengthAdjust='spacingAndGlyphs'>C:22 N:3 O:2</text>
<rect x='259.83' y='63.95' width='387.93' height='533.40' style='stroke-width: 1.71; stroke: #34495E;' />
</g>
<g clip-path='url(#cpMC4wMHw4NjQuMDB8MC4wMHw3MjAuMDA=)'>
<polyline points='259.83,597.35 259.83,63.95 ' style='stroke-width: 1.07; stroke: #34495E; stroke-linecap: butt;' />
<text x='255.35' y='488.16' text-anchor='end' style='font-size: 9.00px;fill: #34495E; font-family: "Arial";' textLength='25.50px' lengthAdjust='spacingAndGlyphs'>Row 1</text>
<text x='255.35' y='399.99' text-anchor='end' style='font-size: 9.00px;fill: #34495E; font-family: "Arial";' textLength='25.50px' lengthAdjust='spacingAndGlyphs'>Row 2</text>
<text x='255.35' y='311.83' text-anchor='end' style='font-size: 9.00px;fill: #34495E; font-family: "Arial";' textLength='25.50px' lengthAdjust='spacingAndGlyphs'>Row 3</text>
<text x='255.35' y='223.66' text-anchor='end' style='font-size: 9.00px;fill: #34495E; font-family: "Arial";' textLength='25.50px' lengthAdjust='spacingAndGlyphs'>Row 4</text>
<text x='255.35' y='135.50' text-anchor='end' style='font-size: 9.00px;fill: #34495E; font-family: "Arial";' textLength='25.50px' lengthAdjust='spacingAndGlyphs'>Row 5</text>
<polyline points='259.83,597.35 647.76,597.35 ' style='stroke-width: 1.07; stroke: #34495E; stroke-linecap: butt;' />
<text x='321.55' y='608.27' text-anchor='middle' style='font-size: 9.00px;fill: #34495E; font-family: "Arial";' textLength='38.50px' lengthAdjust='spacingAndGlyphs'>Column 1</text>
<text x='409.71' y='608.27' text-anchor='middle' style='font-size: 9.00px;fill: #34495E; font-family: "Arial";' textLength='38.50px' lengthAdjust='spacingAndGlyphs'>Column 2</text>
<text x='497.88' y='608.27' text-anchor='middle' style='font-size: 9.00px;fill: #34495E; font-family: "Arial";' textLength='38.50px' lengthAdjust='spacingAndGlyphs'>Column 3</text>
<text x='586.04' y='608.27' text-anchor='middle' style='font-size: 9.00px;fill: #34495E; font-family: "Arial";' textLength='38.50px' lengthAdjust='spacingAndGlyphs'>Column 4</text>
<text x='453.80' y='621.25' text-anchor='middle' style='font-size: 12.00px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='131.28px' lengthAdjust='spacingAndGlyphs'>Grid Position (Column)</text>
<text transform='translate(224.84,330.65) rotate(-90)' text-anchor='middle' style='font-size: 12.00px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='111.95px' lengthAdjust='spacingAndGlyphs'>Grid Position (Row)</text>
<text x='394.27' y='656.89' text-anchor='middle' style='font-size: 10.00px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='53.91px' lengthAdjust='spacingAndGlyphs'>Complexity</text>
<text x='394.27' y='667.69' text-anchor='middle' style='font-size: 10.00px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='27.80px' lengthAdjust='spacingAndGlyphs'>Score</text>
<image width='115.20' height='11.52' x='336.67' y='673.72' preserveAspectRatio='none' xlink:href='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAABCAYAAABkOJMpAAAAn0lEQVQ4ja2UyxHDIAxEn+gmXaatVLc5ALYCC04mPnn99FsxDPF4vgQiBCCQAAgEnS3jVdeviEOfuZHipLirB4ih/u549heamdWGjedzaMeyB8e2Hjbsrxm/eq//sj3mOQEEUNq9KO3MSuM7PdaOfJXv8u7QeZ+rfBzXqfmmVsbDoufELzxj+n/ONXs4Pu1kmFaz+7uQ4/2NmefWXpW9AYDc2CUc14a7AAAAAElFTkSuQmCC'/>
<polyline points='355.15,681.79 355.15,685.24 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='375.47,681.79 375.47,685.24 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='395.79,681.79 395.79,685.24 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='416.12,681.79 416.12,685.24 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='436.44,681.79 436.44,685.24 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='355.15,677.18 355.15,673.72 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='375.47,677.18 375.47,673.72 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='395.79,677.18 395.79,673.72 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='416.12,677.18 416.12,673.72 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='436.44,677.18 436.44,673.72 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<text x='355.15' y='695.96' text-anchor='middle' style='font-size: 8.00px;fill: #34495E; font-family: "Arial";' textLength='8.91px' lengthAdjust='spacingAndGlyphs'>20</text>
<text x='375.47' y='695.96' text-anchor='middle' style='font-size: 8.00px;fill: #34495E; font-family: "Arial";' textLength='8.91px' lengthAdjust='spacingAndGlyphs'>30</text>
<text x='395.79' y='695.96' text-anchor='middle' style='font-size: 8.00px;fill: #34495E; font-family: "Arial";' textLength='8.91px' lengthAdjust='spacingAndGlyphs'>40</text>
<text x='416.12' y='695.96' text-anchor='middle' style='font-size: 8.00px;fill: #34495E; font-family: "Arial";' textLength='8.91px' lengthAdjust='spacingAndGlyphs'>50</text>
<text x='436.44' y='695.96' text-anchor='middle' style='font-size: 8.00px;fill: #34495E; font-family: "Arial";' textLength='8.91px' lengthAdjust='spacingAndGlyphs'>60</text>
<text x='516.38' y='656.89' text-anchor='middle' style='font-size: 10.00px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='35.00px' lengthAdjust='spacingAndGlyphs'>Carbon</text>
<text x='516.38' y='667.69' text-anchor='middle' style='font-size: 10.00px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='28.88px' lengthAdjust='spacingAndGlyphs'>Count</text>
<circle cx='470.47' cy='684.41' r='6.64' style='stroke-width: 1.70; stroke: #000000; stroke-opacity: 0.80; fill: #000000; fill-opacity: 0.80;' />
<circle cx='507.39' cy='684.41' r='7.93' style='stroke-width: 1.70; stroke: #000000; stroke-opacity: 0.80; fill: #000000; fill-opacity: 0.80;' />
<circle cx='546.35' cy='684.41' r='8.89' style='stroke-width: 1.70; stroke: #000000; stroke-opacity: 0.80; fill: #000000; fill-opacity: 0.80;' />
<text x='484.09' y='687.27' style='font-size: 8.00px;fill: #34495E; font-family: "Arial";' textLength='8.91px' lengthAdjust='spacingAndGlyphs'>10</text>
<text x='521.78' y='687.27' style='font-size: 8.00px;fill: #34495E; font-family: "Arial";' textLength='8.91px' lengthAdjust='spacingAndGlyphs'>15</text>
<text x='562.01' y='687.27' style='font-size: 8.00px;fill: #34495E; font-family: "Arial";' textLength='8.91px' lengthAdjust='spacingAndGlyphs'>20</text>
<text x='453.80' y='46.69' text-anchor='middle' style='font-size: 11.00px;fill: #34495E; font-family: "Arial";' textLength='331.95px' lengthAdjust='spacingAndGlyphs'>20 Key Scaffolds from Chemical Database with Complexity Mapping</text>
<text x='453.80' y='15.72' text-anchor='middle' style='font-size: 15.00px; font-weight: bold;fill: #34495E; font-family: "Arial";' textLength='389.62px' lengthAdjust='spacingAndGlyphs'>Representative Molecular Scaffolds Structure Analysis</text>
<text x='647.76' y='713.33' text-anchor='end' style='font-size: 8.00px;fill: #34495E; font-family: "Arial";' textLength='244.12px' lengthAdjust='spacingAndGlyphs'>Data Source: ChEMBL Database | Visualization: ZK Research Group</text>
</g>
</g>
</svg>
