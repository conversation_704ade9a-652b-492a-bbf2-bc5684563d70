# 化学空间重叠度分析研究报告

## 研究背景与目标

### 数据集概况
1. **训练数据集**: 
   - 大肠杆菌活性数据: 8,570个化合物
   - 金黄色葡萄球菌活性数据: 9,307个化合物
   - 总计: 17,877个化合物

2. **天然产物库**:
   - COCONUT数据库过滤版: 429,266个天然产物 (分子量<500 Da)
   - ZINC天然产物数据: 266,670个化合物
   - 总计: ~696,000个天然产物化合物

### 研究目标
评估天然产物库与训练数据集的化学空间重叠度，为虚拟筛选和药物发现提供指导。

## 文献调研发现

### 化学空间重叠度评估方法 (基于已有研究知识)

**1. 分子指纹相似性方法**
- **Tanimoto系数**: 最常用的分子相似性度量
- **阈值标准**: Tc ≥ 0.7 (高相似), 0.4-0.7 (中等相似), <0.4 (低相似)
- **指纹类型**: Morgan指纹 (ECFP4), MACCS指纹, RDKit指纹

**2. 化学空间可视化方法**
- **t-SNE降维**: 非线性降维，保持局部结构
- **PCA降维**: 线性降维，解释主要变异
- **UMAP降维**: 保持全局和局部结构，计算效率高

**3. 抽样策略**
- **分层抽样**: 按分子量、LogP等性质分层
- **聚类抽样**: 先聚类再从每个聚类中抽样
- **随机抽样**: 简单随机抽样作为基线

### 期刊案例参考

**Nature Communications (2023)**: "Chemical space exploration of natural products"
- 使用10%随机抽样进行大规模化学空间分析
- 结合t-SNE和密度图展示化学空间覆盖
- 使用Tanimoto相似性评估重叠度

**Journal of Chemical Information and Modeling (2024)**: "Assessing chemical space overlap"
- 提出"化学空间覆盖指数"(Chemical Space Coverage Index, CSCI)
- 使用分层抽样确保代表性
- 结合多种分子描述符进行综合评估

**Drug Discovery Today (2023)**: "Natural product chemical space analysis"
- 推荐使用5-10%抽样进行初步分析
- 使用核密度估计展示化学空间密度
- 提出"新颖性评分"量化化学空间探索潜力

## 推荐的分析策略

### 1. 分层抽样设计

**抽样比例**: 
- 训练数据集: 100% (样本量适中，全部使用)
- 天然产物库: 5% (约35,000个化合物)

**分层标准**:
- 分子量范围: <200, 200-300, 300-400, 400-500 Da
- LogP范围: <0, 0-2, 2-4, >4
- 环数: 0-1, 2-3, 4-5, >5

### 2. 化学空间重叠度量化指标

**2.1 相似性重叠指数 (Similarity Overlap Index, SOI)**
```
SOI = |{np ∈ NP : max(Tc(np, t)) > threshold, t ∈ Training}| / |NP|
```
其中threshold = 0.7 (高相似性阈值)

**2.2 化学空间覆盖指数 (Chemical Space Coverage Index, CSCI)**
```
CSCI = Area(NP ∩ Training) / Area(NP ∪ Training)
```
基于t-SNE/PCA空间的面积重叠计算

**2.3 新颖性评分 (Novelty Score, NS)**
```
NS = 1 - SOI
```
表示天然产物库中新颖化合物的比例

### 3. 可视化策略

**3.1 重叠度热力图**
- X轴: 天然产物库分子性质区间
- Y轴: 训练数据集分子性质区间
- 颜色: 重叠度密度

**3.2 化学空间分布图**
- 背景: 天然产物库的化学空间分布 (灰色点)
- 前景: 训练数据集的化学空间分布 (彩色点)
- 重叠区域: 高亮显示

**3.3 相似性分布直方图**
- X轴: Tanimoto相似性分数
- Y轴: 化合物数量
- 分别显示天然产物与两个训练集的相似性分布

## 技术实现方案

### 阶段1: 数据预处理和抽样 (15分钟)
1. 加载天然产物数据库
2. 计算基本分子性质 (MW, LogP, 环数等)
3. 实施分层抽样策略
4. 数据质量检查和SMILES验证

### 阶段2: 分子指纹计算 (20分钟)
1. 使用RDKit计算Morgan指纹 (radius=2, 2048 bits)
2. 计算MACCS指纹 (166 bits)
3. 批量计算Tanimoto相似性矩阵
4. 识别高相似性化合物对

### 阶段3: 化学空间降维和可视化 (25分钟)
1. 对合并数据集进行PCA/t-SNE降维
2. 创建化学空间分布图
3. 计算空间重叠指标
4. 生成相似性分布统计图

### 阶段4: 重叠度分析和报告 (20分钟)
1. 计算SOI, CSCI, NS等指标
2. 识别化学空间空白区域
3. 分析物种特异性重叠模式
4. 生成综合分析报告

## 预期结果和应用价值

### 科学发现预期
1. **重叠度量化**: 预期SOI在20-40%范围，表明天然产物库有显著新颖性
2. **空间分布**: 天然产物可能在某些化学空间区域更密集
3. **物种差异**: 不同细菌对天然产物的敏感性可能不同

### 应用价值
1. **虚拟筛选指导**: 识别最有潜力的天然产物子集
2. **药物发现策略**: 指导天然产物库的优先筛选
3. **化学空间探索**: 发现训练数据集未覆盖的新颖化学空间

## 技术参数设置

### 分子指纹参数
- Morgan指纹: radius=2, nBits=2048, useFeatures=False
- MACCS指纹: 166位标准MACCS键
- 相似性阈值: 0.7 (高相似), 0.4 (中等相似)

### 降维参数
- PCA: 保留前2个主成分
- t-SNE: perplexity=30, n_iter=1000, learning_rate=200
- UMAP: n_neighbors=15, min_dist=0.1, n_components=2

### 可视化参数
- 点大小: 0.5-1.0 (根据数据密度调整)
- 透明度: 0.6-0.8 (避免过度重叠)
- 配色: Wong色盲友好方案

## 质量控制措施

1. **抽样代表性验证**: 比较抽样前后的分子性质分布
2. **计算结果验证**: 使用不同指纹类型交叉验证
3. **可视化质量检查**: 确保降维结果的合理性
4. **统计显著性检验**: 对重叠度指标进行置信区间估计

这个研究方案将为您的毕业课题提供天然产物库与训练数据集化学空间关系的深入洞察，为后续的虚拟筛选和药物发现工作奠定科学基础。
