# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-08-03
# 描述: 绘制Halicin相似性分析的Nature风格图表

# 加载必要的包
library(ggplot2)
library(dplyr)
library(scales)
library(RColorBrewer)
library(viridis)

# 设置工作目录
setwd("E:/毕业课题/毕业课题/模式数据/03_图表输出/Halicin相似性分析图表")

# 读取数据
data <- read.csv("halicin_similarity_data.csv", stringsAsFactors = FALSE)

# Nature风格主题函数
theme_nature <- function(base_size = 7) {
  theme_bw(base_size = base_size) +
  theme(
    panel.grid.major = element_blank(),
    panel.grid.minor = element_blank(),
    panel.border = element_rect(colour = "black", fill = NA, linewidth = 0.5),
    plot.title = element_text(hjust = 0.5, size = rel(1.1), color = "black"),
    axis.text = element_text(color = "black", size = rel(1.0)),
    axis.title = element_text(color = "black", size = rel(1.0)),
    axis.ticks = element_line(colour = "black", linewidth = 0.5),
    legend.key = element_blank(),
    plot.margin = margin(5, 5, 5, 5, "mm")
  )
}

# Wong配色方案
wong_colors <- c("#000000", "#E69F00", "#56B4E9", "#009E73", 
                 "#F0E442", "#0072B2", "#D55E00", "#CC79A7")

# 按相似性排序数据
data <- data %>% arrange(desc(Tanimoto_Sim_To_Halicin))

# 1. 水平条形图 - 展示各化合物与Halicin的相似性分数
p1 <- ggplot(data, aes(x = reorder(Chinese_Name, Tanimoto_Sim_To_Halicin), 
                       y = Tanimoto_Sim_To_Halicin)) +
  geom_col(fill = wong_colors[6], alpha = 0.8, width = 0.7) +  # 使用蓝色
  geom_text(aes(label = sprintf("%.3f", Tanimoto_Sim_To_Halicin)), 
            hjust = -0.1, size = 2.0, color = "black") +
  coord_flip() +
  labs(
    title = "Compounds Similarity to Halicin (Tanimoto Index)",
    subtitle = "化合物与Halicin的Tanimoto相似性指数",
    x = "化合物名称 (Compound Name)",
    y = "Tanimoto相似性指数 (Tanimoto Similarity Index)"
  ) +
  scale_y_continuous(
    limits = c(0, 0.4),
    breaks = seq(0, 0.4, 0.05),
    expand = c(0, 0)
  ) +
  theme_nature()

# 显示并保存图表1
print(p1)
ggsave("Halicin_Similarity_BarChart.pdf", plot = p1, 
       width = 120, height = 80, units = "mm", device = cairo_pdf, dpi = 300)
ggsave("Halicin_Similarity_BarChart.png", plot = p1, 
       width = 120, height = 80, units = "mm", dpi = 300)
ggsave("Halicin_Similarity_BarChart.svg", plot = p1, 
       width = 120, height = 80, units = "mm")

# 2. 点图 - 更清晰的相似性展示
p2 <- ggplot(data, aes(x = Tanimoto_Sim_To_Halicin, 
                       y = reorder(Chinese_Name, Tanimoto_Sim_To_Halicin))) +
  geom_segment(aes(x = 0, xend = Tanimoto_Sim_To_Halicin, 
                   y = Chinese_Name, yend = Chinese_Name), 
               color = wong_colors[7], linewidth = 0.5) +  # 使用橙红色线条
  geom_point(color = wong_colors[6], size = 3, alpha = 0.9) +  # 使用蓝色点
  geom_text(aes(label = sprintf("%.3f", Tanimoto_Sim_To_Halicin)), 
            hjust = -0.2, size = 2.0, color = "black") +
  labs(
    title = "Halicin Structural Similarity Analysis",
    subtitle = "Halicin结构相似性分析",
    x = "Tanimoto相似性指数 (Tanimoto Similarity Index)",
    y = "化合物名称 (Compound Name)"
  ) +
  scale_x_continuous(
    limits = c(0, 0.4),
    breaks = seq(0, 0.4, 0.05),
    expand = c(0, 0)
  ) +
  theme_nature()

# 显示并保存图表2
print(p2)
ggsave("Halicin_Similarity_DotPlot.pdf", plot = p2, 
       width = 120, height = 80, units = "mm", device = cairo_pdf, dpi = 300)
ggsave("Halicin_Similarity_DotPlot.png", plot = p2, 
       width = 120, height = 80, units = "mm", dpi = 300)
ggsave("Halicin_Similarity_DotPlot.svg", plot = p2, 
       width = 120, height = 80, units = "mm")

# 3. 热图风格的相似性可视化
# 创建相似性等级
data$Similarity_Level <- cut(data$Tanimoto_Sim_To_Halicin, 
                            breaks = c(0, 0.2, 0.25, 0.3, 0.4),
                            labels = c("低相似性", "中等相似性", "高相似性", "极高相似性"),
                            include.lowest = TRUE)

p3 <- ggplot(data, aes(x = 1, y = reorder(Chinese_Name, Tanimoto_Sim_To_Halicin), 
                       fill = Tanimoto_Sim_To_Halicin)) +
  geom_tile(color = "white", linewidth = 0.5) +
  geom_text(aes(label = sprintf("%.3f", Tanimoto_Sim_To_Halicin)), 
            color = "white", size = 2.5, fontface = "bold") +
  scale_fill_viridis_c(name = "相似性指数\nSimilarity Index", 
                       option = "plasma", direction = 1) +
  labs(
    title = "Halicin Similarity Heatmap",
    subtitle = "Halicin相似性热图",
    x = "",
    y = "化合物名称 (Compound Name)"
  ) +
  theme_nature() +
  theme(
    axis.text.x = element_blank(),
    axis.ticks.x = element_blank(),
    legend.position = "right"
  )

# 显示并保存图表3
print(p3)
ggsave("Halicin_Similarity_Heatmap.pdf", plot = p3, 
       width = 100, height = 80, units = "mm", device = cairo_pdf, dpi = 300)
ggsave("Halicin_Similarity_Heatmap.png", plot = p3, 
       width = 100, height = 80, units = "mm", dpi = 300)
ggsave("Halicin_Similarity_Heatmap.svg", plot = p3, 
       width = 100, height = 80, units = "mm")

# 输出数据摘要
cat("=== Halicin相似性数据分析摘要 ===\n")
cat("化合物总数:", nrow(data), "\n")
cat("最高相似性:", max(data$Tanimoto_Sim_To_Halicin), 
    " (", data$Chinese_Name[which.max(data$Tanimoto_Sim_To_Halicin)], ")\n")
cat("最低相似性:", min(data$Tanimoto_Sim_To_Halicin), 
    " (", data$Chinese_Name[which.min(data$Tanimoto_Sim_To_Halicin)], ")\n")
cat("平均相似性:", round(mean(data$Tanimoto_Sim_To_Halicin), 3), "\n")
cat("相似性标准差:", round(sd(data$Tanimoto_Sim_To_Halicin), 3), "\n")

cat("\n=== 相似性等级分布 ===\n")
print(table(data$Similarity_Level))

cat("\n图表已保存为以下格式:\n")
cat("1. 条形图: Halicin_Similarity_BarChart (PDF/PNG/SVG)\n")
cat("2. 点图: Halicin_Similarity_DotPlot (PDF/PNG/SVG)\n")
cat("3. 热图: Halicin_Similarity_Heatmap (PDF/PNG/SVG)\n")
