# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-14
# 描述: 分子骨架表格式展示 - 学术级图表（无需外部化学包）

# 清理环境
rm(list = ls())

# 加载必要的包
suppressMessages({
    library(ggplot2)
    library(dplyr)
    library(readr)
    library(gridExtra)
    library(grid)
    library(stringr)
    library(grDevices)
    library(scales)
})

# 设置随机种子
set.seed(42)

cat("=== 分子骨架表格式展示 ===\n")

# 读取分子骨架数据
scaffold_data <- read_csv("01_原始数据/分子骨架数据/scaffold_smiles_data.csv", 
                         show_col_types = FALSE)

cat("数据加载完成，共", nrow(scaffold_data), "个分子骨架\n")

# 中国风学术配色方案
chinese_academic_colors <- c(
    "#C0392B",  # 朱红 (Primary)
    "#2980B9",  # 靛蓝 (Secondary) 
    "#27AE60",  # 石绿 (Accent 2)
    "#F1C40F",  # 藤黄 (Accent 3)
    "#A0522D",  # 赭石 (Accent 1)
    "#34495E",  # 青金 (Accent 4)
    "#73C6B6",  # 天青 (Tertiary)
    "#E74C3C"   # 备用红色
)

# Nature风格主题函数
theme_nature_chinese <- function(base_size = 10) {
    theme_minimal(base_size = base_size) +
        theme(
            # 标题设置
            plot.title = element_text(
                hjust = 0.5, 
                size = rel(1.4), 
                color = "#34495E",
                face = "bold",
                margin = margin(b = 20)
            ),
            plot.subtitle = element_text(
                hjust = 0.5, 
                color = "#34495E",
                size = rel(1.1),
                margin = margin(b = 15)
            ),
            
            # 坐标轴设置
            axis.title = element_text(
                size = rel(1.1), 
                color = "#34495E",
                face = "bold"
            ),
            axis.text = element_text(
                color = "#34495E",
                size = rel(0.9)
            ),
            axis.line = element_line(
                color = "#34495E", 
                linewidth = 0.5
            ),
            
            # 面板设置
            panel.grid = element_blank(),
            panel.border = element_rect(
                colour = "#34495E", 
                fill = NA, 
                linewidth = 0.8
            ),
            
            # 图例设置
            legend.title = element_text(
                color = "#34495E",
                face = "bold",
                size = rel(1.0)
            ),
            legend.text = element_text(
                color = "#34495E",
                size = rel(0.9)
            ),
            legend.key = element_blank(),
            
            # 背景设置
            plot.background = element_rect(
                fill = "#FDFEFE", 
                color = NA
            ),
            panel.background = element_rect(
                fill = "#FDFEFE", 
                color = NA
            ),
            
            # 分面设置
            strip.background = element_rect(
                fill = "white", 
                colour = "#34495E",
                linewidth = 0.5
            ),
            strip.text = element_text(
                color = "#34495E",
                face = "bold",
                size = rel(1.0)
            ),
            
            # 图表说明
            plot.caption = element_text(
                hjust = 1,
                color = "#34495E",
                size = rel(0.8),
                margin = margin(t = 10)
            )
        )
}

# 处理分子数据并计算特征
process_scaffold_data <- function(data) {
    cat("处理分子骨架数据...\n")
    
    processed_data <- data %>%
        mutate(
            # 计算基本分子特征
            smiles_length = nchar(SMILES),
            carbon_count = str_count(SMILES, "[Cc]"),
            nitrogen_count = str_count(SMILES, "[Nn]"),
            oxygen_count = str_count(SMILES, "[Oo]"),
            sulfur_count = str_count(SMILES, "[Ss]"),
            fluorine_count = str_count(SMILES, "[Ff]"),
            ring_count = str_count(SMILES, "[0-9]"),
            
            # 计算分子复杂度评分
            complexity_score = smiles_length + carbon_count * 0.5 + 
                              nitrogen_count * 2 + oxygen_count * 1.5 +
                              sulfur_count * 2 + fluorine_count * 1.5,
            
            # 分类分子类型
            molecule_type = case_when(
                str_detect(SMILES, "c1ccccc1") & smiles_length < 15 ~ "Simple Aromatic",
                str_detect(SMILES, "O=c") ~ "Carbonyl Compound",
                str_detect(SMILES, "N") ~ "Nitrogen-containing",
                TRUE ~ "Other"
            ),
            
            # 为网格布局计算位置
            row = ceiling(row_number() / 4),  # 4列布局
            col = ((row_number() - 1) %% 4) + 1,
            
            # 简化SMILES显示
            smiles_short = ifelse(nchar(SMILES) > 25, 
                                 paste0(substr(SMILES, 1, 25), "..."), 
                                 SMILES)
        ) %>%
        arrange(complexity_score)
    
    return(processed_data)
}

# 处理数据
mol_processed <- process_scaffold_data(scaffold_data)

cat("数据处理完成\n")
cat("分子复杂度范围:", round(min(mol_processed$complexity_score), 2), 
    "到", round(max(mol_processed$complexity_score), 2), "\n")

# 创建分子骨架信息表格图
create_scaffold_table_plot <- function(data) {
    cat("创建分子骨架信息表格图...\n")
    
    # 创建表格式布局的图表
    p <- ggplot(data, aes(x = col, y = -row)) +
        
        # 添加背景矩形
        geom_tile(aes(fill = molecule_type), 
                 alpha = 0.3, 
                 color = "#34495E", 
                 linewidth = 0.5,
                 width = 0.9,
                 height = 0.9) +
        
        # 添加分子ID标签
        geom_text(aes(label = Scaffold_ID),
                 vjust = -1.8,
                 hjust = 0.5,
                 size = 3.5,
                 color = "#34495E",
                 fontface = "bold") +
        
        # 添加SMILES结构
        geom_text(aes(label = smiles_short),
                 vjust = -0.5,
                 hjust = 0.5,
                 size = 2.2,
                 color = "#34495E",
                 family = "mono") +
        
        # 添加分子特征信息
        geom_text(aes(label = paste0("C:", carbon_count, " N:", nitrogen_count, 
                                   " O:", oxygen_count)),
                 vjust = 0.8,
                 hjust = 0.5,
                 size = 2.0,
                 color = "#34495E",
                 alpha = 0.8) +
        
        # 添加复杂度评分
        geom_text(aes(label = paste0("Score: ", round(complexity_score, 1))),
                 vjust = 2.0,
                 hjust = 0.5,
                 size = 1.8,
                 color = chinese_academic_colors[1],  # 朱红色
                 fontface = "bold") +
        
        # 颜色映射
        scale_fill_manual(
            values = chinese_academic_colors[1:4],
            name = "Molecule Type",
            guide = guide_legend(
                title.position = "top",
                title.hjust = 0.5,
                ncol = 2
            )
        ) +
        
        # 坐标轴设置
        scale_x_continuous(
            limits = c(0.5, 4.5),
            breaks = 1:4,
            labels = paste("Column", 1:4)
        ) +
        scale_y_continuous(
            limits = c(-6, -0.5),
            breaks = -5:-1,
            labels = paste("Row", 1:5)
        ) +
        
        # 标签和标题
        labs(
            title = "Representative Molecular Scaffolds - Structural Information Table",
            subtitle = "20 Key Scaffolds with Chemical Properties and Complexity Analysis",
            x = "Grid Layout (Column Position)",
            y = "Grid Layout (Row Position)",
            caption = "Data Source: ChEMBL Database | Analysis: ZK Research Group | Generated with R/ggplot2"
        ) +
        
        # 应用主题
        theme_nature_chinese(base_size = 10) +
        
        # 图例位置和样式
        theme(
            legend.position = "bottom",
            legend.box = "horizontal",
            legend.margin = margin(t = 15),
            legend.key.size = unit(0.8, "cm")
        ) +
        
        # 确保比例协调
        coord_fixed(ratio = 1)
    
    return(p)
}

# 创建主图表
main_plot <- create_scaffold_table_plot(mol_processed)

cat("主图表创建完成\n")

# 创建输出目录
output_dir <- "03_图表输出/论文级图表"
if (!dir.exists(output_dir)) {
    dir.create(output_dir, recursive = TRUE)
}

cat("开始保存图表文件...\n")

# 保存高质量PNG图片
png_file <- file.path(output_dir, "分子骨架信息表格_Nature风格.png")
ggsave(
    filename = png_file,
    plot = main_plot,
    width = 14,
    height = 12,
    dpi = 300,
    bg = "white",
    device = "png"
)

cat("PNG图片保存完成:", png_file, "\n")

# 保存PDF矢量图
pdf_file <- file.path(output_dir, "分子骨架信息表格_Nature风格.pdf")
ggsave(
    filename = pdf_file,
    plot = main_plot,
    width = 14,
    height = 12,
    bg = "white",
    device = "pdf"
)

cat("PDF矢量图保存完成:", pdf_file, "\n")

# 保存SVG矢量图
svg_file <- file.path(output_dir, "分子骨架信息表格_Nature风格.svg")
ggsave(
    filename = svg_file,
    plot = main_plot,
    width = 14,
    height = 12,
    bg = "white",
    device = "svg"
)

cat("SVG矢量图保存完成:", svg_file, "\n")

# 创建分子特征统计图
create_feature_summary_plot <- function(data) {
    cat("创建分子特征统计图...\n")
    
    # 准备统计数据
    feature_summary <- data %>%
        summarise(
            avg_carbon = mean(carbon_count),
            avg_nitrogen = mean(nitrogen_count),
            avg_oxygen = mean(oxygen_count),
            avg_complexity = mean(complexity_score),
            .groups = 'drop'
        )
    
    # 创建特征分布图
    p1 <- ggplot(data, aes(x = carbon_count, y = complexity_score)) +
        geom_point(aes(color = molecule_type, size = oxygen_count), 
                  alpha = 0.7) +
        geom_smooth(method = "lm", se = FALSE, color = chinese_academic_colors[2]) +
        scale_color_manual(values = chinese_academic_colors[1:4]) +
        scale_size_continuous(range = c(2, 6)) +
        labs(
            title = "Molecular Complexity vs Carbon Content",
            x = "Carbon Count",
            y = "Complexity Score",
            color = "Molecule Type",
            size = "Oxygen Count"
        ) +
        theme_nature_chinese()
    
    return(p1)
}

# 创建特征统计图
feature_plot <- create_feature_summary_plot(mol_processed)

# 保存特征统计图
feature_png <- file.path(output_dir, "分子特征统计图_Nature风格.png")
ggsave(
    filename = feature_png,
    plot = feature_plot,
    width = 10,
    height = 8,
    dpi = 300,
    bg = "white",
    device = "png"
)

cat("特征统计图保存完成:", feature_png, "\n")

cat("\n=== 分子骨架表格式展示完成 ===\n")
cat("生成的文件:\n")
cat("- 主表格图 PNG:", png_file, "\n")
cat("- 主表格图 PDF:", pdf_file, "\n")
cat("- 主表格图 SVG:", svg_file, "\n")
cat("- 特征统计图:", feature_png, "\n")
cat("图表特点:\n")
cat("- 表格式分子信息展示\n")
cat("- 分子类型颜色分类\n")
cat("- 复杂度评分系统\n")
cat("- Nature期刊风格设计\n")
cat("- 中国风配色方案\n")
cat("- 高分辨率输出(300 DPI)\n")
