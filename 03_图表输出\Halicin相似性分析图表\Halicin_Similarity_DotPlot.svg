<?xml version='1.0' encoding='UTF-8' ?>
<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='340.16pt' height='226.77pt' viewBox='0 0 340.16 226.77'>
<g class='svglite'>
<defs>
  <style type='text/css'><![CDATA[
    .svglite line, .svglite polyline, .svglite polygon, .svglite path, .svglite rect, .svglite circle {
      fill: none;
      stroke: #000000;
      stroke-linecap: round;
      stroke-linejoin: round;
      stroke-miterlimit: 10.00;
    }
    .svglite text {
      white-space: pre;
    }
    .svglite g.glyphgroup path {
      fill: inherit;
      stroke: none;
    }
  ]]></style>
</defs>
<rect width='100%' height='100%' style='stroke: none; fill: #FFFFFF;'/>
<defs>
  <clipPath id='cpMC4wMHwzNDAuMTZ8MC4wMHwyMjYuNzc='>
    <rect x='0.00' y='0.00' width='340.16' height='226.77' />
  </clipPath>
</defs>
<g clip-path='url(#cpMC4wMHwzNDAuMTZ8MC4wMHwyMjYuNzc=)'>
<rect x='0.00' y='0.000000000000028' width='340.16' height='226.77' style='stroke-width: 0.68; stroke: #FFFFFF; fill: #FFFFFF;' />
</g>
<defs>
  <clipPath id='cpNjAuNTN8MzI1Ljk4fDM0Ljc1fDE5NC43Ng=='>
    <rect x='60.53' y='34.75' width='265.45' height='160.01' />
  </clipPath>
</defs>
<g clip-path='url(#cpNjAuNTN8MzI1Ljk4fDM0Ljc1fDE5NC43Ng==)'>
<rect x='60.53' y='34.75' width='265.45' height='160.01' style='stroke-width: 0.68; stroke: none; fill: #FFFFFF;' />
<line x1='60.53' y1='48.08' x2='305.79' y2='48.08' style='stroke-width: 1.07; stroke: #D55E00; stroke-linecap: butt;' />
<line x1='60.53' y1='70.31' x2='248.56' y2='70.31' style='stroke-width: 1.07; stroke: #D55E00; stroke-linecap: butt;' />
<line x1='60.53' y1='92.53' x2='219.23' y2='92.53' style='stroke-width: 1.07; stroke: #D55E00; stroke-linecap: butt;' />
<line x1='60.53' y1='114.75' x2='202.74' y2='114.75' style='stroke-width: 1.07; stroke: #D55E00; stroke-linecap: butt;' />
<line x1='60.53' y1='136.98' x2='200.92' y2='136.98' style='stroke-width: 1.07; stroke: #D55E00; stroke-linecap: butt;' />
<line x1='60.53' y1='159.20' x2='195.72' y2='159.20' style='stroke-width: 1.07; stroke: #D55E00; stroke-linecap: butt;' />
<line x1='60.53' y1='181.42' x2='193.26' y2='181.42' style='stroke-width: 1.07; stroke: #D55E00; stroke-linecap: butt;' />
<circle cx='305.79' cy='48.08' r='3.56' style='stroke-width: 0.71; stroke: #0072B2; stroke-opacity: 0.90; fill: #0072B2; fill-opacity: 0.90;' />
<circle cx='248.56' cy='70.31' r='3.56' style='stroke-width: 0.71; stroke: #0072B2; stroke-opacity: 0.90; fill: #0072B2; fill-opacity: 0.90;' />
<circle cx='219.23' cy='92.53' r='3.56' style='stroke-width: 0.71; stroke: #0072B2; stroke-opacity: 0.90; fill: #0072B2; fill-opacity: 0.90;' />
<circle cx='202.74' cy='114.75' r='3.56' style='stroke-width: 0.71; stroke: #0072B2; stroke-opacity: 0.90; fill: #0072B2; fill-opacity: 0.90;' />
<circle cx='200.92' cy='136.98' r='3.56' style='stroke-width: 0.71; stroke: #0072B2; stroke-opacity: 0.90; fill: #0072B2; fill-opacity: 0.90;' />
<circle cx='195.72' cy='159.20' r='3.56' style='stroke-width: 0.71; stroke: #0072B2; stroke-opacity: 0.90; fill: #0072B2; fill-opacity: 0.90;' />
<circle cx='193.26' cy='181.42' r='3.56' style='stroke-width: 0.71; stroke: #0072B2; stroke-opacity: 0.90; fill: #0072B2; fill-opacity: 0.90;' />
<text x='308.63' y='50.12' style='font-size: 5.69px; font-family: "Arial";' textLength='14.20px' lengthAdjust='spacingAndGlyphs'>0.370</text>
<text x='251.40' y='72.34' style='font-size: 5.69px; font-family: "Arial";' textLength='14.20px' lengthAdjust='spacingAndGlyphs'>0.283</text>
<text x='222.07' y='94.57' style='font-size: 5.69px; font-family: "Arial";' textLength='14.20px' lengthAdjust='spacingAndGlyphs'>0.239</text>
<text x='205.58' y='116.79' style='font-size: 5.69px; font-family: "Arial";' textLength='14.20px' lengthAdjust='spacingAndGlyphs'>0.214</text>
<text x='203.76' y='139.01' style='font-size: 5.69px; font-family: "Arial";' textLength='14.20px' lengthAdjust='spacingAndGlyphs'>0.212</text>
<text x='198.56' y='161.24' style='font-size: 5.69px; font-family: "Arial";' textLength='14.20px' lengthAdjust='spacingAndGlyphs'>0.204</text>
<text x='196.10' y='183.46' style='font-size: 5.69px; font-family: "Arial";' textLength='14.20px' lengthAdjust='spacingAndGlyphs'>0.200</text>
<rect x='60.53' y='34.75' width='265.45' height='160.01' style='stroke-width: 1.07;' />
</g>
<g clip-path='url(#cpMC4wMHwzNDAuMTZ8MC4wMHwyMjYuNzc=)'>
<text x='57.40' y='183.93' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='28.00px' lengthAdjust='spacingAndGlyphs'>氨基噻唑</text>
<text x='57.40' y='161.70' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='28.00px' lengthAdjust='spacingAndGlyphs'>塞克硝唑</text>
<text x='57.40' y='139.48' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='21.00px' lengthAdjust='spacingAndGlyphs'>甲硝唑</text>
<text x='57.40' y='117.26' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='21.00px' lengthAdjust='spacingAndGlyphs'>罗硝唑</text>
<text x='57.40' y='95.03' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='35.00px' lengthAdjust='spacingAndGlyphs'>二甲硝咪唑</text>
<text x='57.40' y='72.81' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='28.00px' lengthAdjust='spacingAndGlyphs'>硝唑尼特</text>
<text x='57.40' y='50.59' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='21.00px' lengthAdjust='spacingAndGlyphs'>硝硫胺</text>
<polyline points='58.79,181.42 60.53,181.42 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='58.79,159.20 60.53,159.20 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='58.79,136.98 60.53,136.98 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='58.79,114.75 60.53,114.75 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='58.79,92.53 60.53,92.53 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='58.79,70.31 60.53,70.31 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='58.79,48.08 60.53,48.08 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='60.53,196.50 60.53,194.76 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='93.72,196.50 93.72,194.76 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='126.90,196.50 126.90,194.76 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='160.08,196.50 160.08,194.76 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='193.26,196.50 193.26,194.76 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='226.44,196.50 226.44,194.76 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='259.62,196.50 259.62,194.76 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='292.80,196.50 292.80,194.76 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='325.98,196.50 325.98,194.76 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<text x='60.53' y='202.91' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='13.61px' lengthAdjust='spacingAndGlyphs'>0.00</text>
<text x='93.72' y='202.91' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='13.61px' lengthAdjust='spacingAndGlyphs'>0.05</text>
<text x='126.90' y='202.91' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='13.61px' lengthAdjust='spacingAndGlyphs'>0.10</text>
<text x='160.08' y='202.91' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='13.61px' lengthAdjust='spacingAndGlyphs'>0.15</text>
<text x='193.26' y='202.91' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='13.61px' lengthAdjust='spacingAndGlyphs'>0.20</text>
<text x='226.44' y='202.91' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='13.61px' lengthAdjust='spacingAndGlyphs'>0.25</text>
<text x='259.62' y='202.91' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='13.61px' lengthAdjust='spacingAndGlyphs'>0.30</text>
<text x='292.80' y='202.91' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='13.61px' lengthAdjust='spacingAndGlyphs'>0.35</text>
<text x='325.98' y='202.91' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='13.61px' lengthAdjust='spacingAndGlyphs'>0.40</text>
<text x='193.26' y='211.13' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='147.77px' lengthAdjust='spacingAndGlyphs'>Tanimoto相似性指数 (Tanimoto Similarity Index)</text>
<text transform='translate(19.18,114.75) rotate(-90)' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='96.44px' lengthAdjust='spacingAndGlyphs'>化合物名称 (Compound Name)</text>
<text x='60.53' y='29.79' style='font-size: 7.00px; font-family: "Arial";' textLength='70.03px' lengthAdjust='spacingAndGlyphs'>Halicin结构相似性分析</text>
<text x='193.26' y='19.68' text-anchor='middle' style='font-size: 7.70px; font-family: "Arial";' textLength='121.78px' lengthAdjust='spacingAndGlyphs'>Halicin Structural Similarity Analysis</text>
</g>
</g>
</svg>
