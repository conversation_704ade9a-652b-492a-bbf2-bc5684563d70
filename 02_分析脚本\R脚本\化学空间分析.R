# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-11
# 描述: 化学空间分布可视化 - 大肠杆菌vs金黄色葡萄球菌 (最终版)

# 加载必要的包
suppressMessages({
    library(ggplot2)
    library(dplyr)
    library(readr)
    library(gridExtra)
    library(grid)
})

# 设置随机种子
set.seed(42)

# Nature风格主题 - 使用系统默认字体
theme_nature_clean <- function(base_size = 8) {
    theme_bw(base_size = base_size) +
        theme(
            panel.grid.major = element_blank(),
            panel.grid.minor = element_blank(),
            panel.border = element_rect(colour = "black", fill = NA, linewidth = 0.5),
            plot.title = element_text(hjust = 0.5, size = rel(1.2)),
            axis.text = element_text(color = "black"),
            axis.ticks = element_line(colour = "black", linewidth = 0.5),
            legend.key = element_blank(),
            strip.background = element_rect(fill = "white", colour = "black")
        )
}

# Wong配色方案
wong_colors <- c("#000000", "#E69F00", "#56B4E9", "#009E73",
                 "#F0E442", "#0072B2", "#D55E00", "#CC79A7")

# 计算简化分子特征
calculate_features <- function(smiles_vector) {
    cat("计算分子特征...\n")
    
    # 计算基本特征
    features <- data.frame(
        smiles_length = nchar(smiles_vector),
        carbon_count = stringr::str_count(smiles_vector, "C"),
        nitrogen_count = stringr::str_count(smiles_vector, "N"),
        oxygen_count = stringr::str_count(smiles_vector, "O"),
        sulfur_count = stringr::str_count(smiles_vector, "S"),
        fluorine_count = stringr::str_count(smiles_vector, "F"),
        chlorine_count = stringr::str_count(smiles_vector, "Cl"),
        ring_count = stringr::str_count(smiles_vector, "[0-9]"),
        aromatic_count = stringr::str_count(smiles_vector, "[cnos]"),
        double_bond_count = stringr::str_count(smiles_vector, "="),
        stereochemistry_count = stringr::str_count(smiles_vector, "@"),
        branch_count = stringr::str_count(smiles_vector, "\\(")
    )
    
    return(features)
}

# 数据加载和预处理
load_data <- function() {
    cat("正在加载数据集...\n")
    
    # 加载数据集1 (金黄色葡萄球菌)
    data1 <- read_csv("cleaned_data-12-9-no-duplicate.csv", show_col_types = FALSE)
    data1$Species <- "S. aureus"
    cat("数据集1加载成功:", nrow(data1), "行 (金黄色葡萄球菌)\n")
    
    # 加载数据集2 (大肠杆菌)
    data2 <- read_csv("filtered_ecoli_9000-nosalt.csv", show_col_types = FALSE)
    data2$Species <- "E. coli"
    cat("数据集2加载成功:", nrow(data2), "行 (大肠杆菌)\n")
    
    # 数据预处理
    data1_clean <- data1 %>%
        select(Smiles, Activity, Species) %>%
        filter(!is.na(Smiles), Smiles != "", nchar(Smiles) > 5)
    
    data2_clean <- data2 %>%
        select(Smiles, Activity, Species) %>%
        filter(!is.na(Smiles), Smiles != "", nchar(Smiles) > 5)
    
    # 合并数据
    combined_data <- rbind(data1_clean, data2_clean)
    
    # 转换Activity为因子
    combined_data$Activity <- factor(combined_data$Activity, 
                                   levels = c(0, 1), 
                                   labels = c("Inactive", "Active"))
    
    # 创建组合标签
    combined_data$Species_Activity <- paste(combined_data$Species, 
                                          combined_data$Activity, 
                                          sep = " - ")
    
    cat("合并数据完成:", nrow(combined_data), "个化合物\n")
    return(combined_data)
}

# 执行PCA降维
do_pca <- function(features) {
    cat("执行PCA降维分析...\n")
    
    # 标准化特征
    features_scaled <- scale(features)
    
    # 执行PCA
    pca_result <- prcomp(features_scaled, center = FALSE, scale. = FALSE)
    
    # 计算解释方差比例
    var_explained <- summary(pca_result)$importance[2, 1:2] * 100
    
    cat("PC1解释方差:", round(var_explained[1], 2), "%\n")
    cat("PC2解释方差:", round(var_explained[2], 2), "%\n")
    cat("前两个主成分总解释方差:", round(sum(var_explained), 2), "%\n")
    
    return(list(coords = pca_result$x[, 1:2], var_explained = var_explained))
}

# 创建化学空间可视化图
make_plots <- function(data, pca_result) {
    cat("创建化学空间可视化图...\n")
    
    # 准备绘图数据
    plot_data <- data.frame(
        PC1 = pca_result$coords[, 1],
        PC2 = pca_result$coords[, 2],
        Species = data$Species,
        Activity = data$Activity,
        Species_Activity = data$Species_Activity
    )
    
    # 创建轴标签
    pc1_label <- paste0("PC1 (", round(pca_result$var_explained[1], 1), "% variance)")
    pc2_label <- paste0("PC2 (", round(pca_result$var_explained[2], 1), "% variance)")
    
    # 图1: 按物种分类
    p1 <- ggplot(plot_data, aes(x = PC1, y = PC2, color = Species)) +
        geom_point(size = 0.8, alpha = 0.7) +
        scale_color_manual(values = wong_colors[1:2],
                          name = "Species",
                          labels = c("E. coli", "S. aureus")) +
        labs(title = "Chemical Space Distribution by Species",
             x = pc1_label,
             y = pc2_label) +
        theme_nature_clean() +
        guides(color = guide_legend(override.aes = list(size = 2, alpha = 1)))
    
    # 图2: 按活性分类
    p2 <- ggplot(plot_data, aes(x = PC1, y = PC2, color = Activity)) +
        geom_point(size = 0.8, alpha = 0.7) +
        scale_color_manual(values = wong_colors[3:4],
                          name = "Activity") +
        labs(title = "Chemical Space Distribution by Activity",
             x = pc1_label,
             y = pc2_label) +
        theme_nature_clean() +
        guides(color = guide_legend(override.aes = list(size = 2, alpha = 1)))
    
    # 图3: 按物种和活性组合
    p3 <- ggplot(plot_data, aes(x = PC1, y = PC2, color = Species_Activity)) +
        geom_point(size = 0.8, alpha = 0.7) +
        scale_color_manual(values = wong_colors[1:4],
                          name = "Species & Activity") +
        labs(title = "Chemical Space: Species and Activity",
             x = pc1_label,
             y = pc2_label) +
        theme_nature_clean() +
        guides(color = guide_legend(override.aes = list(size = 2, alpha = 1))) +
        theme(legend.text = element_text(size = 7))
    
    # 图4: 分面图
    p4 <- ggplot(plot_data, aes(x = PC1, y = PC2, color = Activity)) +
        geom_point(size = 0.8, alpha = 0.7) +
        scale_color_manual(values = wong_colors[3:4],
                          name = "Activity") +
        labs(title = "Chemical Space by Species (Faceted)",
             x = pc1_label,
             y = pc2_label) +
        theme_nature_clean() +
        facet_wrap(~Species, scales = "free") +
        guides(color = guide_legend(override.aes = list(size = 2, alpha = 1)))
    
    return(list(p1 = p1, p2 = p2, p3 = p3, p4 = p4, data = plot_data))
}

# 计算统计信息
calc_stats <- function(plot_data) {
    cat("\n=== 化学空间分布统计 ===\n")
    
    # 按物种和活性组合统计
    combined_stats <- plot_data %>%
        group_by(Species, Activity) %>%
        summarise(
            count = n(),
            percentage = n() / nrow(plot_data) * 100,
            .groups = 'drop'
        )
    
    cat("按物种和活性组合分布:\n")
    print(combined_stats)
    
    return(combined_stats)
}

# 主函数
main <- function() {
    cat("开始化学空间分析\n")
    cat("分析对象: 大肠杆菌 vs 金黄色葡萄球菌活性数据\n")
    cat(rep("=", 60), "\n")
    
    # 加载数据
    data <- load_data()
    
    # 计算分子特征
    features <- calculate_features(data$Smiles)
    
    # 执行PCA
    pca_result <- do_pca(features)
    
    # 创建可视化图
    plots <- make_plots(data, pca_result)
    
    # 保存图表
    cat("\n保存化学空间图表...\n")
    
    ggsave("ChemSpace_Species.pdf", plots$p1, 
           width = 120, height = 90, units = "mm", device = "pdf")
    ggsave("ChemSpace_Species.png", plots$p1, 
           width = 120, height = 90, units = "mm", dpi = 300)
    
    ggsave("ChemSpace_Activity.pdf", plots$p2, 
           width = 120, height = 90, units = "mm", device = "pdf")
    ggsave("ChemSpace_Activity.png", plots$p2, 
           width = 120, height = 90, units = "mm", dpi = 300)
    
    ggsave("ChemSpace_Combined.pdf", plots$p3, 
           width = 140, height = 100, units = "mm", device = "pdf")
    ggsave("ChemSpace_Combined.png", plots$p3, 
           width = 140, height = 100, units = "mm", dpi = 300)
    
    ggsave("ChemSpace_Faceted.pdf", plots$p4, 
           width = 183, height = 90, units = "mm", device = "pdf")
    ggsave("ChemSpace_Faceted.png", plots$p4, 
           width = 183, height = 90, units = "mm", dpi = 300)
    
    # 创建组合图 - 简化版本避免字体问题
    cat("创建组合图表...\n")
    combined_plot <- grid.arrange(plots$p1, plots$p2, plots$p3, plots$p4,
                                 ncol = 2, nrow = 2)
    
    ggsave("ChemSpace_All_Combined.pdf", combined_plot, 
           width = 183, height = 150, units = "mm", device = "pdf")
    ggsave("ChemSpace_All_Combined.png", combined_plot, 
           width = 183, height = 150, units = "mm", dpi = 300)
    
    # 计算统计信息
    stats <- calc_stats(plots$data)
    
    cat("\n=== 化学空间分析完成 ===\n")
    cat("生成的文件:\n")
    cat("- ChemSpace_Species.pdf/.png (按物种分类)\n")
    cat("- ChemSpace_Activity.pdf/.png (按活性分类)\n")
    cat("- ChemSpace_Combined.pdf/.png (物种+活性组合)\n")
    cat("- ChemSpace_Faceted.pdf/.png (分面图)\n")
    cat("- ChemSpace_All_Combined.pdf/.png (所有图组合)\n")
    
    cat("\n数据统计摘要:\n")
    cat("总化合物数:", nrow(data), "\n")
    cat("大肠杆菌化合物:", sum(data$Species == "E. coli"), "\n")
    cat("金黄色葡萄球菌化合物:", sum(data$Species == "S. aureus"), "\n")
    cat("活性化合物:", sum(data$Activity == "Active"), "\n")
    cat("非活性化合物:", sum(data$Activity == "Inactive"), "\n")
    
    # 保存统计结果
    write.csv(stats, "ChemSpace_statistics.csv", row.names = FALSE)
    cat("统计结果已保存至: ChemSpace_statistics.csv\n")
}

# 执行主函数
main()
