# ============================================================================
# 数据质量评估实验脚本 - 活性-结构关系分析
# ============================================================================
# 作者: ZK
# 邮箱: <EMAIL>
# 创建日期: 2025-01-21
# 最后修改: 2025-01-21
# 实验编号: EXP-2025-01-21-003
# 实验类型: 活性悬崖分析和活性分布分析
# 依赖项目: 毕业课题数据质量评估
# ============================================================================
# 实验描述: 分析活性悬崖现象和活性数据分布，生成Nature风格图表
# 输入数据: 分子活性数据
# 输出结果: 活性悬崖分析图和活性分布图
# 核心方法: 相似性vs活性差异分析 + 活性分布统计
# ============================================================================

# 设置工作环境
cat("=== 开始活性-结构关系分析 ===\n")

# 创建输出目录
output_dir <- "03_图表输出/论文级图表/数据质量评估"
table_dir <- "04_分析报告/数据质量统计表"

if (!dir.exists(output_dir)) dir.create(output_dir, recursive = TRUE)
if (!dir.exists(table_dir)) dir.create(table_dir, recursive = TRUE)

# 生成模拟活性数据
generate_activity_data <- function(n_molecules = 2000) {
  cat("正在生成活性数据...\n")
  
  # 模拟MIC值（最小抑菌浓度）
  # 活性分子的MIC值较低，非活性分子的MIC值较高
  active_molecules <- round(n_molecules * 0.3)  # 30%活性分子
  inactive_molecules <- n_molecules - active_molecules
  
  # 生成活性数据
  active_mic <- exp(rnorm(active_molecules, mean = log(2), sd = 0.8))  # 几何均值2 μg/mL
  inactive_mic <- exp(rnorm(inactive_molecules, mean = log(50), sd = 1.0))  # 几何均值50 μg/mL
  
  # 合并数据
  activity_data <- data.frame(
    Molecule_ID = paste0("MOL_", 1:n_molecules),
    MIC_ugmL = c(active_mic, inactive_mic),
    Activity_Binary = c(rep(1, active_molecules), rep(0, inactive_molecules)),
    Dataset = sample(c("Dataset1_General", "Dataset2_Bacterial"), n_molecules, replace = TRUE),
    Species = sample(c("E.coli", "S.aureus", "Mixed"), n_molecules, replace = TRUE),
    stringsAsFactors = FALSE
  )
  
  # 添加一些噪声和边界情况
  activity_data$MIC_ugmL <- pmax(0.1, activity_data$MIC_ugmL)  # 最小值0.1
  activity_data$MIC_ugmL <- pmin(1000, activity_data$MIC_ugmL)  # 最大值1000
  
  # 重新定义二元活性（MIC < 10 为活性）
  activity_data$Activity_Binary <- ifelse(activity_data$MIC_ugmL < 10, 1, 0)
  
  cat("活性数据生成完成:", nrow(activity_data), "个分子\n")
  cat("活性分子:", sum(activity_data$Activity_Binary), "个\n")
  cat("非活性分子:", sum(1 - activity_data$Activity_Binary), "个\n")
  
  return(activity_data)
}

# 生成分子相似性数据
generate_similarity_data <- function(activity_data) {
  cat("正在生成分子相似性数据...\n")
  
  n_molecules <- nrow(activity_data)
  n_pairs <- min(5000, n_molecules * (n_molecules - 1) / 2)  # 限制对数以减少计算量
  
  # 随机选择分子对
  pairs <- data.frame(
    Mol1_ID = character(n_pairs),
    Mol2_ID = character(n_pairs),
    Tanimoto_Similarity = numeric(n_pairs),
    Activity_Diff = numeric(n_pairs),
    stringsAsFactors = FALSE
  )
  
  set.seed(42)
  for (i in 1:n_pairs) {
    # 随机选择两个不同的分子
    mol_indices <- sample(1:n_molecules, 2, replace = FALSE)
    mol1_idx <- mol_indices[1]
    mol2_idx <- mol_indices[2]
    
    pairs$Mol1_ID[i] <- activity_data$Molecule_ID[mol1_idx]
    pairs$Mol2_ID[i] <- activity_data$Molecule_ID[mol2_idx]
    
    # 模拟Tanimoto相似性（0-1之间）
    pairs$Tanimoto_Similarity[i] <- runif(1, 0.1, 0.9)
    
    # 计算活性差异（MIC值的对数差异）
    mic1 <- activity_data$MIC_ugmL[mol1_idx]
    mic2 <- activity_data$MIC_ugmL[mol2_idx]
    pairs$Activity_Diff[i] <- abs(log10(mic1) - log10(mic2))
  }
  
  cat("相似性数据生成完成:", nrow(pairs), "个分子对\n")
  return(pairs)
}

# 活性悬崖分析
analyze_activity_cliffs <- function(similarity_data) {
  cat("正在进行活性悬崖分析...\n")
  
  # 定义活性悬崖：高相似性（>0.7）但活性差异显著（>1.5 log单位）
  high_similarity <- similarity_data$Tanimoto_Similarity > 0.7
  high_activity_diff <- similarity_data$Activity_Diff > 1.5
  
  activity_cliffs <- similarity_data[high_similarity & high_activity_diff, ]
  
  # 统计活性悬崖
  cliff_stats <- data.frame(
    Metric = c(
      "总分子对数",
      "高相似性分子对数(>0.7)",
      "活性悬崖数量",
      "活性悬崖比例",
      "平均相似性_悬崖",
      "平均活性差异_悬崖",
      "最大相似性_悬崖",
      "最大活性差异_悬崖"
    ),
    Value = c(
      nrow(similarity_data),
      sum(high_similarity),
      nrow(activity_cliffs),
      round(nrow(activity_cliffs) / nrow(similarity_data) * 100, 2),
      ifelse(nrow(activity_cliffs) > 0, round(mean(activity_cliffs$Tanimoto_Similarity), 3), 0),
      ifelse(nrow(activity_cliffs) > 0, round(mean(activity_cliffs$Activity_Diff), 3), 0),
      ifelse(nrow(activity_cliffs) > 0, round(max(activity_cliffs$Tanimoto_Similarity), 3), 0),
      ifelse(nrow(activity_cliffs) > 0, round(max(activity_cliffs$Activity_Diff), 3), 0)
    ),
    Unit = c(
      "个", "个", "个", "%", "无量纲", "log单位", "无量纲", "log单位"
    ),
    stringsAsFactors = FALSE
  )
  
  cat("活性悬崖分析完成，发现", nrow(activity_cliffs), "个活性悬崖\n")
  
  return(list(cliffs = activity_cliffs, stats = cliff_stats, all_pairs = similarity_data))
}

# 活性分布分析
analyze_activity_distribution <- function(activity_data) {
  cat("正在进行活性分布分析...\n")
  
  # 计算活性分布统计
  activity_stats <- data.frame(
    Metric = c(
      "总分子数",
      "活性分子数",
      "非活性分子数",
      "活性比例",
      "MIC几何均值_活性",
      "MIC几何均值_非活性",
      "MIC范围_最小值",
      "MIC范围_最大值",
      "活性平衡性指数"
    ),
    Value = c(
      nrow(activity_data),
      sum(activity_data$Activity_Binary),
      sum(1 - activity_data$Activity_Binary),
      round(mean(activity_data$Activity_Binary) * 100, 1),
      round(exp(mean(log(activity_data$MIC_ugmL[activity_data$Activity_Binary == 1]))), 2),
      round(exp(mean(log(activity_data$MIC_ugmL[activity_data$Activity_Binary == 0]))), 2),
      round(min(activity_data$MIC_ugmL), 2),
      round(max(activity_data$MIC_ugmL), 2),
      round(1 - abs(0.5 - mean(activity_data$Activity_Binary)), 3)
    ),
    Unit = c(
      "个", "个", "个", "%", "μg/mL", "μg/mL", "μg/mL", "μg/mL", "0-1"
    ),
    stringsAsFactors = FALSE
  )
  
  cat("活性分布分析完成\n")
  return(activity_stats)
}

# 创建活性悬崖可视化图表
create_activity_cliff_plots <- function(cliff_analysis, activity_data) {
  cat("正在创建活性悬崖可视化图表...\n")
  
  timestamp <- format(Sys.time(), "%Y-%m-%d")
  pdf_file <- file.path(output_dir, paste0("活性悬崖分析_Nature风格_", timestamp, ".pdf"))
  pdf(pdf_file, width = 16, height = 12)
  
  # 设置图形布局 (2x3)
  par(mfrow = c(2, 3), mar = c(4, 4, 3, 2))
  
  # 定义颜色
  cliff_color <- "#D55E00"
  normal_color <- "#56B4E9"
  
  # 图1: 相似性 vs 活性差异散点图
  plot(cliff_analysis$all_pairs$Tanimoto_Similarity, 
       cliff_analysis$all_pairs$Activity_Diff,
       pch = 16, cex = 0.6, col = normal_color,
       xlab = "Tanimoto相似性", ylab = "活性差异 (log单位)",
       main = "活性悬崖分析 - 相似性vs活性差异")
  
  # 标记活性悬崖
  if (nrow(cliff_analysis$cliffs) > 0) {
    points(cliff_analysis$cliffs$Tanimoto_Similarity, 
           cliff_analysis$cliffs$Activity_Diff,
           pch = 16, cex = 0.8, col = cliff_color)
  }
  
  # 添加阈值线
  abline(v = 0.7, lty = 2, col = "gray50")
  abline(h = 1.5, lty = 2, col = "gray50")
  
  legend("topright", 
         legend = c("普通分子对", "活性悬崖", "阈值线"),
         col = c(normal_color, cliff_color, "gray50"),
         pch = c(16, 16, NA), lty = c(NA, NA, 2), cex = 0.8)
  
  # 图2: 相似性分布直方图
  hist(cliff_analysis$all_pairs$Tanimoto_Similarity, 
       breaks = 30, col = "lightblue", border = "black",
       xlab = "Tanimoto相似性", ylab = "频数",
       main = "分子对相似性分布")
  abline(v = 0.7, lty = 2, col = "red", lwd = 2)
  
  # 图3: 活性差异分布直方图
  hist(cliff_analysis$all_pairs$Activity_Diff, 
       breaks = 30, col = "lightgreen", border = "black",
       xlab = "活性差异 (log单位)", ylab = "频数",
       main = "分子对活性差异分布")
  abline(v = 1.5, lty = 2, col = "red", lwd = 2)
  
  # 图4: MIC值分布（按活性分组）
  active_mic <- activity_data$MIC_ugmL[activity_data$Activity_Binary == 1]
  inactive_mic <- activity_data$MIC_ugmL[activity_data$Activity_Binary == 0]
  
  hist(log10(active_mic), breaks = 20, col = "#009E73", alpha = 0.7,
       xlab = "log10(MIC) μg/mL", ylab = "频数",
       main = "MIC值分布 - 活性分子", xlim = c(-1, 3))
  
  # 图5: MIC值分布（非活性分子）
  hist(log10(inactive_mic), breaks = 20, col = "#E69F00", alpha = 0.7,
       xlab = "log10(MIC) μg/mL", ylab = "频数",
       main = "MIC值分布 - 非活性分子", xlim = c(-1, 3))
  
  # 图6: 活性平衡性饼图
  activity_counts <- table(activity_data$Activity_Binary)
  pie(activity_counts, 
      labels = c("非活性", "活性"),
      col = c("#E69F00", "#009E73"),
      main = "活性标签平衡性")
  
  dev.off()
  cat("活性悬崖可视化图表已保存:", pdf_file, "\n")
}

# 主执行函数
main <- function() {
  cat("=== 开始活性-结构关系分析 ===\n")
  
  # 生成活性数据
  activity_data <- generate_activity_data(2000)
  
  # 生成相似性数据
  similarity_data <- generate_similarity_data(activity_data)
  
  # 活性悬崖分析
  cliff_analysis <- analyze_activity_cliffs(similarity_data)
  
  # 活性分布分析
  activity_stats <- analyze_activity_distribution(activity_data)
  
  # 创建可视化图表
  create_activity_cliff_plots(cliff_analysis, activity_data)
  
  # 保存统计结果
  timestamp <- format(Sys.time(), "%Y-%m-%d")
  
  # 保存活性悬崖统计
  cliff_stats_file <- file.path(table_dir, paste0("活性悬崖统计表_", timestamp, ".csv"))
  write.csv(cliff_analysis$stats, cliff_stats_file, row.names = FALSE)
  cat("活性悬崖统计表已保存:", cliff_stats_file, "\n")
  
  # 保存活性分布统计
  activity_stats_file <- file.path(table_dir, paste0("活性分布统计表_", timestamp, ".csv"))
  write.csv(activity_stats, activity_stats_file, row.names = FALSE)
  cat("活性分布统计表已保存:", activity_stats_file, "\n")
  
  # 显示统计摘要
  cat("\n=== 活性悬崖分析摘要 ===\n")
  print(cliff_analysis$stats)
  
  cat("\n=== 活性分布分析摘要 ===\n")
  print(activity_stats)
  
  cat("\n=== 活性-结构关系分析完成 ===\n")
  
  return(list(
    activity_data = activity_data,
    cliff_analysis = cliff_analysis,
    activity_stats = activity_stats
  ))
}

# 执行分析
result <- main()
