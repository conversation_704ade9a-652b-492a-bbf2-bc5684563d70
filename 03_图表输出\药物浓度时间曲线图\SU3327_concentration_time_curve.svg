<?xml version='1.0' encoding='UTF-8' ?>
<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='252.28pt' height='170.08pt' viewBox='0 0 252.28 170.08'>
<g class='svglite'>
<defs>
  <style type='text/css'><![CDATA[
    .svglite line, .svglite polyline, .svglite polygon, .svglite path, .svglite rect, .svglite circle {
      fill: none;
      stroke: #000000;
      stroke-linecap: round;
      stroke-linejoin: round;
      stroke-miterlimit: 10.00;
    }
    .svglite text {
      white-space: pre;
    }
    .svglite g.glyphgroup path {
      fill: inherit;
      stroke: none;
    }
  ]]></style>
</defs>
<rect width='100%' height='100%' style='stroke: none; fill: #FFFFFF;'/>
<defs>
  <clipPath id='cpMC4wMHwyNTIuMjh8MC4wMHwxNzAuMDg='>
    <rect x='0.00' y='0.00' width='252.28' height='170.08' />
  </clipPath>
</defs>
<g clip-path='url(#cpMC4wMHwyNTIuMjh8MC4wMHwxNzAuMDg=)'>
<rect x='0.00' y='0.000000000000028' width='252.28' height='170.08' style='stroke-width: 0.97; stroke: #FFFFFF; fill: #FFFFFF;' />
</g>
<defs>
  <clipPath id='cpNDMuODJ8MjQyLjMyfDI1LjE0fDEzMy42OA=='>
    <rect x='43.82' y='25.14' width='198.50' height='108.54' />
  </clipPath>
</defs>
<g clip-path='url(#cpNDMuODJ8MjQyLjMyfDI1LjE0fDEzMy42OA==)'>
<rect x='43.82' y='25.14' width='198.50' height='108.54' style='stroke-width: 0.97; stroke: none; fill: #FFFFFF;' />
<polyline points='52.58,131.03 96.36,34.67 140.15,79.01 227.73,115.41 ' style='stroke-width: 3.20; stroke: #2E86AB; stroke-opacity: 0.90; stroke-linecap: butt;' />
<circle cx='52.58' cy='131.03' r='4.09' style='stroke-width: 0.71; stroke: none; fill: #2E86AB; fill-opacity: 0.95;' />
<circle cx='96.36' cy='34.67' r='4.09' style='stroke-width: 0.71; stroke: none; fill: #2E86AB; fill-opacity: 0.95;' />
<circle cx='140.15' cy='79.01' r='4.09' style='stroke-width: 0.71; stroke: none; fill: #2E86AB; fill-opacity: 0.95;' />
<circle cx='227.73' cy='115.41' r='4.09' style='stroke-width: 0.71; stroke: none; fill: #2E86AB; fill-opacity: 0.95;' />
<text x='52.58' y='123.70' text-anchor='middle' style='font-size: 8.54px; font-weight: bold;fill: #4A4A4A; font-family: "Arial";' textLength='22.78px' lengthAdjust='spacingAndGlyphs'>0 ppb</text>
<text x='96.36' y='27.33' text-anchor='middle' style='font-size: 8.54px; font-weight: bold;fill: #4A4A4A; font-family: "Arial";' textLength='32.28px' lengthAdjust='spacingAndGlyphs'>728 ppb</text>
<text x='140.15' y='71.68' text-anchor='middle' style='font-size: 8.54px; font-weight: bold;fill: #4A4A4A; font-family: "Arial";' textLength='32.28px' lengthAdjust='spacingAndGlyphs'>393 ppb</text>
<text x='227.73' y='108.08' text-anchor='middle' style='font-size: 8.54px; font-weight: bold;fill: #4A4A4A; font-family: "Arial";' textLength='31.81px' lengthAdjust='spacingAndGlyphs'>118 ppb</text>
<rect x='43.82' y='25.14' width='198.50' height='108.54' style='stroke-width: 1.07;' />
</g>
<g clip-path='url(#cpMC4wMHwyNTIuMjh8MC4wMHwxNzAuMDg=)'>
<text x='39.34' y='134.62' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='5.56px' lengthAdjust='spacingAndGlyphs'>0</text>
<text x='39.34' y='121.38' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>100</text>
<text x='39.34' y='108.14' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>200</text>
<text x='39.34' y='94.90' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>300</text>
<text x='39.34' y='81.67' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>400</text>
<text x='39.34' y='68.43' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>500</text>
<text x='39.34' y='55.19' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>600</text>
<text x='39.34' y='41.96' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>700</text>
<text x='39.34' y='28.72' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>800</text>
<polyline points='41.33,131.03 43.82,131.03 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,117.80 43.82,117.80 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,104.56 43.82,104.56 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,91.32 43.82,91.32 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,78.09 43.82,78.09 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,64.85 43.82,64.85 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,51.61 43.82,51.61 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,38.38 43.82,38.38 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,25.14 43.82,25.14 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='52.58,136.17 52.58,133.68 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='96.36,136.17 96.36,133.68 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='140.15,136.17 140.15,133.68 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='183.94,136.17 183.94,133.68 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='227.73,136.17 227.73,133.68 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<text x='52.58' y='145.33' text-anchor='middle' style='font-size: 10.00px; font-family: "Arial";' textLength='5.56px' lengthAdjust='spacingAndGlyphs'>0</text>
<text x='96.36' y='145.33' text-anchor='middle' style='font-size: 10.00px; font-family: "Arial";' textLength='11.12px' lengthAdjust='spacingAndGlyphs'>15</text>
<text x='140.15' y='145.33' text-anchor='middle' style='font-size: 10.00px; font-family: "Arial";' textLength='11.12px' lengthAdjust='spacingAndGlyphs'>30</text>
<text x='183.94' y='145.33' text-anchor='middle' style='font-size: 10.00px; font-family: "Arial";' textLength='11.12px' lengthAdjust='spacingAndGlyphs'>45</text>
<text x='227.73' y='145.33' text-anchor='middle' style='font-size: 10.00px; font-family: "Arial";' textLength='11.12px' lengthAdjust='spacingAndGlyphs'>60</text>
<text x='143.07' y='157.80' text-anchor='middle' style='font-size: 11.00px; font-family: "Arial";' textLength='52.12px' lengthAdjust='spacingAndGlyphs'>Time (min)</text>
<text transform='translate(17.84,79.41) rotate(-90)' text-anchor='middle' style='font-size: 11.00px; font-family: "Arial";' textLength='140.12px' lengthAdjust='spacingAndGlyphs'>SU3327 Concentration (ppb)</text>
<text x='143.07' y='17.84' text-anchor='middle' style='font-size: 11.00px; font-family: "Arial";' textLength='179.23px' lengthAdjust='spacingAndGlyphs'>SU3327 Drug Concentration vs Time</text>
</g>
</g>
</svg>
