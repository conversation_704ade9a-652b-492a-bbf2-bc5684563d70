<?xml version='1.0' encoding='UTF-8' ?>
<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='252.28pt' height='170.08pt' viewBox='0 0 252.28 170.08'>
<g class='svglite'>
<defs>
  <style type='text/css'><![CDATA[
    .svglite line, .svglite polyline, .svglite polygon, .svglite path, .svglite rect, .svglite circle {
      fill: none;
      stroke: #000000;
      stroke-linecap: round;
      stroke-linejoin: round;
      stroke-miterlimit: 10.00;
    }
    .svglite text {
      white-space: pre;
    }
    .svglite g.glyphgroup path {
      fill: inherit;
      stroke: none;
    }
  ]]></style>
</defs>
<rect width='100%' height='100%' style='stroke: none; fill: #FFFFFF;'/>
<defs>
  <clipPath id='cpMC4wMHwyNTIuMjh8MC4wMHwxNzAuMDg='>
    <rect x='0.00' y='0.00' width='252.28' height='170.08' />
  </clipPath>
</defs>
<g clip-path='url(#cpMC4wMHwyNTIuMjh8MC4wMHwxNzAuMDg=)'>
<rect x='0.00' y='0.000000000000028' width='252.28' height='170.08' style='stroke-width: 0.68; stroke: #FFFFFF; fill: #FFFFFF;' />
</g>
<defs>
  <clipPath id='cpMzcuMjF8MjM4LjExfDI0Ljc4fDEzOC4wNg=='>
    <rect x='37.21' y='24.78' width='200.90' height='113.28' />
  </clipPath>
</defs>
<g clip-path='url(#cpMzcuMjF8MjM4LjExfDI0Ljc4fDEzOC4wNg==)'>
<rect x='37.21' y='24.78' width='200.90' height='113.28' style='stroke-width: 0.68; stroke: none; fill: #FFFFFF;' />
<polyline points='51.56,131.77 94.61,40.14 137.66,82.30 223.76,116.92 ' style='stroke-width: 2.13; stroke: #0072B2; stroke-linecap: butt;' />
<circle cx='51.56' cy='131.77' r='2.49' style='stroke-width: 0.71; stroke: none; fill: #0072B2;' />
<circle cx='94.61' cy='40.14' r='2.49' style='stroke-width: 0.71; stroke: none; fill: #0072B2;' />
<circle cx='137.66' cy='82.30' r='2.49' style='stroke-width: 0.71; stroke: none; fill: #0072B2;' />
<circle cx='223.76' cy='116.92' r='2.49' style='stroke-width: 0.71; stroke: none; fill: #0072B2;' />
<text x='51.56' y='127.51' text-anchor='middle' style='font-size: 5.69px; font-family: "Arial";' textLength='14.20px' lengthAdjust='spacingAndGlyphs'>0 ppb</text>
<text x='94.61' y='35.63' text-anchor='middle' style='font-size: 5.69px; font-family: "Arial";' textLength='20.52px' lengthAdjust='spacingAndGlyphs'>728 ppb</text>
<text x='137.66' y='78.43' text-anchor='middle' style='font-size: 5.69px; font-family: "Arial";' textLength='20.52px' lengthAdjust='spacingAndGlyphs'>393 ppb</text>
<text x='223.76' y='112.41' text-anchor='middle' style='font-size: 5.69px; font-family: "Arial";' textLength='20.09px' lengthAdjust='spacingAndGlyphs'>118 ppb</text>
<rect x='37.21' y='24.78' width='200.90' height='113.28' style='stroke-width: 1.07;' />
</g>
<g clip-path='url(#cpMC4wMHwyNTIuMjh8MC4wMHwxNzAuMDg=)'>
<text x='34.07' y='134.28' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='3.89px' lengthAdjust='spacingAndGlyphs'>0</text>
<text x='34.07' y='121.69' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='11.67px' lengthAdjust='spacingAndGlyphs'>100</text>
<text x='34.07' y='109.10' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='11.67px' lengthAdjust='spacingAndGlyphs'>200</text>
<text x='34.07' y='96.51' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='11.67px' lengthAdjust='spacingAndGlyphs'>300</text>
<text x='34.07' y='83.93' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='11.67px' lengthAdjust='spacingAndGlyphs'>400</text>
<text x='34.07' y='71.34' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='11.67px' lengthAdjust='spacingAndGlyphs'>500</text>
<text x='34.07' y='58.75' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='11.67px' lengthAdjust='spacingAndGlyphs'>600</text>
<text x='34.07' y='46.17' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='11.67px' lengthAdjust='spacingAndGlyphs'>700</text>
<text x='34.07' y='33.58' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='11.67px' lengthAdjust='spacingAndGlyphs'>800</text>
<polyline points='35.46,131.77 37.21,131.77 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='35.46,119.18 37.21,119.18 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='35.46,106.60 37.21,106.60 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='35.46,94.01 37.21,94.01 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='35.46,81.42 37.21,81.42 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='35.46,68.84 37.21,68.84 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='35.46,56.25 37.21,56.25 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='35.46,43.66 37.21,43.66 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='35.46,31.07 37.21,31.07 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='51.56,139.81 51.56,138.06 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='94.61,139.81 94.61,138.06 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='137.66,139.81 137.66,138.06 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='180.71,139.81 180.71,138.06 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='223.76,139.81 223.76,138.06 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<text x='51.56' y='146.21' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='3.89px' lengthAdjust='spacingAndGlyphs'>0</text>
<text x='94.61' y='146.21' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='7.78px' lengthAdjust='spacingAndGlyphs'>15</text>
<text x='137.66' y='146.21' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='7.78px' lengthAdjust='spacingAndGlyphs'>30</text>
<text x='180.71' y='146.21' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='7.78px' lengthAdjust='spacingAndGlyphs'>45</text>
<text x='223.76' y='146.21' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='7.78px' lengthAdjust='spacingAndGlyphs'>60</text>
<text x='137.66' y='154.44' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='33.17px' lengthAdjust='spacingAndGlyphs'>Time (min)</text>
<text transform='translate(19.18,81.42) rotate(-90)' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='89.06px' lengthAdjust='spacingAndGlyphs'>SU3327 Concentration (ppb)</text>
<text x='137.66' y='19.68' text-anchor='middle' style='font-size: 7.70px; font-family: "Arial";' textLength='125.30px' lengthAdjust='spacingAndGlyphs'>SU3327 Drug Concentration vs Time</text>
</g>
</g>
</svg>
