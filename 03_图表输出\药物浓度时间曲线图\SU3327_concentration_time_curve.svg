<?xml version='1.0' encoding='UTF-8' ?>
<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='252.28pt' height='170.08pt' viewBox='0 0 252.28 170.08'>
<g class='svglite'>
<defs>
  <style type='text/css'><![CDATA[
    .svglite line, .svglite polyline, .svglite polygon, .svglite path, .svglite rect, .svglite circle {
      fill: none;
      stroke: #000000;
      stroke-linecap: round;
      stroke-linejoin: round;
      stroke-miterlimit: 10.00;
    }
    .svglite text {
      white-space: pre;
    }
    .svglite g.glyphgroup path {
      fill: inherit;
      stroke: none;
    }
  ]]></style>
</defs>
<rect width='100%' height='100%' style='stroke: none; fill: #FFFFFF;'/>
<defs>
  <clipPath id='cpMC4wMHwyNTIuMjh8MC4wMHwxNzAuMDg='>
    <rect x='0.00' y='0.00' width='252.28' height='170.08' />
  </clipPath>
</defs>
<g clip-path='url(#cpMC4wMHwyNTIuMjh8MC4wMHwxNzAuMDg=)'>
<rect x='0.00' y='0.000000000000028' width='252.28' height='170.08' style='stroke-width: 0.97; stroke: #FFFFFF; fill: #FFFFFF;' />
</g>
<defs>
  <clipPath id='cpNDMuODJ8MjQyLjMyfDI1LjE0fDEzMy42OA=='>
    <rect x='43.82' y='25.14' width='198.50' height='108.54' />
  </clipPath>
</defs>
<g clip-path='url(#cpNDMuODJ8MjQyLjMyfDI1LjE0fDEzMy42OA==)'>
<rect x='43.82' y='25.14' width='198.50' height='108.54' style='stroke-width: 0.97; stroke: none; fill: #FFFFFF;' />
<polyline points='49.74,34.91 94.19,80.36 138.63,117.67 227.51,133.68 ' style='stroke-width: 2.56; stroke: #0072B2; stroke-linecap: butt;' />
<circle cx='49.74' cy='34.91' r='3.02' style='stroke-width: 0.71; stroke: none; fill: #0072B2;' />
<circle cx='94.19' cy='80.36' r='3.02' style='stroke-width: 0.71; stroke: none; fill: #0072B2;' />
<circle cx='138.63' cy='117.67' r='3.02' style='stroke-width: 0.71; stroke: none; fill: #0072B2;' />
<circle cx='227.51' cy='133.68' r='3.02' style='stroke-width: 0.71; stroke: none; fill: #0072B2;' />
<text x='49.74' y='30.84' text-anchor='middle' style='font-size: 7.11px; font-family: "Arial";' textLength='25.69px' lengthAdjust='spacingAndGlyphs'>728 ppb</text>
<text x='94.19' y='76.29' text-anchor='middle' style='font-size: 7.11px; font-family: "Arial";' textLength='25.69px' lengthAdjust='spacingAndGlyphs'>393 ppb</text>
<text x='138.63' y='113.60' text-anchor='middle' style='font-size: 7.11px; font-family: "Arial";' textLength='25.16px' lengthAdjust='spacingAndGlyphs'>118 ppb</text>
<text x='227.51' y='129.61' text-anchor='middle' style='font-size: 7.11px; font-family: "Arial";' textLength='17.78px' lengthAdjust='spacingAndGlyphs'>0 ppb</text>
<rect x='43.82' y='25.14' width='198.50' height='108.54' style='stroke-width: 1.07;' />
</g>
<g clip-path='url(#cpMC4wMHwyNTIuMjh8MC4wMHwxNzAuMDg=)'>
<text x='39.34' y='137.26' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='5.56px' lengthAdjust='spacingAndGlyphs'>0</text>
<text x='39.34' y='123.69' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>100</text>
<text x='39.34' y='110.13' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>200</text>
<text x='39.34' y='96.56' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>300</text>
<text x='39.34' y='82.99' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>400</text>
<text x='39.34' y='69.42' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>500</text>
<text x='39.34' y='55.86' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>600</text>
<text x='39.34' y='42.29' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>700</text>
<text x='39.34' y='28.72' text-anchor='end' style='font-size: 10.00px; font-family: "Arial";' textLength='16.69px' lengthAdjust='spacingAndGlyphs'>800</text>
<polyline points='41.33,133.68 43.82,133.68 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,120.11 43.82,120.11 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,106.55 43.82,106.55 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,92.98 43.82,92.98 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,79.41 43.82,79.41 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,65.84 43.82,65.84 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,52.27 43.82,52.27 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,38.71 43.82,38.71 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='41.33,25.14 43.82,25.14 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='49.74,136.17 49.74,133.68 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='94.19,136.17 94.19,133.68 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='138.63,136.17 138.63,133.68 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='183.07,136.17 183.07,133.68 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='227.51,136.17 227.51,133.68 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<text x='49.74' y='145.33' text-anchor='middle' style='font-size: 10.00px; font-family: "Arial";' textLength='5.56px' lengthAdjust='spacingAndGlyphs'>0</text>
<text x='94.19' y='145.33' text-anchor='middle' style='font-size: 10.00px; font-family: "Arial";' textLength='11.12px' lengthAdjust='spacingAndGlyphs'>15</text>
<text x='138.63' y='145.33' text-anchor='middle' style='font-size: 10.00px; font-family: "Arial";' textLength='11.12px' lengthAdjust='spacingAndGlyphs'>30</text>
<text x='183.07' y='145.33' text-anchor='middle' style='font-size: 10.00px; font-family: "Arial";' textLength='11.12px' lengthAdjust='spacingAndGlyphs'>45</text>
<text x='227.51' y='145.33' text-anchor='middle' style='font-size: 10.00px; font-family: "Arial";' textLength='11.12px' lengthAdjust='spacingAndGlyphs'>60</text>
<text x='143.07' y='157.80' text-anchor='middle' style='font-size: 11.00px; font-family: "Arial";' textLength='54.38px' lengthAdjust='spacingAndGlyphs'>时间 (分钟)</text>
<text transform='translate(17.84,79.41) rotate(-90)' text-anchor='middle' style='font-size: 11.00px; font-family: "Arial";' textLength='90.53px' lengthAdjust='spacingAndGlyphs'>SU3327浓度 (ppb)</text>
<text x='143.07' y='17.84' text-anchor='middle' style='font-size: 11.00px; font-family: "Arial";' textLength='160.78px' lengthAdjust='spacingAndGlyphs'>SU3327药物浓度随时间变化曲线</text>
</g>
</g>
</svg>
