# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-08-03
# 描述: 生成Halicin相似性化合物的分子结构对比图

import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from rdkit import Chem
from rdkit.Chem import Draw, rdMolDescriptors
from rdkit.Chem.Draw import rdMolDraw2D
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import io
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据
data = pd.read_csv('halicin_similarity_data.csv')

# Halicin的SMILES (参考结构)
halicin_smiles = "C1=CC=C(C(=C1)C(=O)NC2=NC=CS2)[N+](=O)[O-]"
halicin_name = "Halicin (参考化合物)"

def draw_molecule_with_info(smiles, name, chinese_name, similarity, img_size=(300, 200)):
    """绘制分子结构并添加信息标签"""
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return None
            
        # 使用RDKit绘制分子
        drawer = rdMolDraw2D.MolDraw2DCairo(img_size[0], img_size[1])
        drawer.DrawMolecule(mol)
        drawer.FinishDrawing()
        
        # 获取图像数据
        img_data = drawer.GetDrawingText()
        img = Image.open(io.BytesIO(img_data))
        
        # 创建新的图像，为文本留出空间
        new_height = img_size[1] + 80
        new_img = Image.new('RGB', (img_size[0], new_height), 'white')
        new_img.paste(img, (0, 0))
        
        # 添加文本信息
        draw = ImageDraw.Draw(new_img)
        try:
            font = ImageFont.truetype("arial.ttf", 12)
            font_small = ImageFont.truetype("arial.ttf", 10)
        except:
            font = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # 添加化合物名称
        draw.text((10, img_size[1] + 5), f"{chinese_name}", fill='black', font=font)
        draw.text((10, img_size[1] + 20), f"({name})", fill='gray', font=font_small)
        
        # 添加相似性分数
        if similarity is not None:
            sim_text = f"相似性: {similarity:.3f}"
            draw.text((10, img_size[1] + 40), sim_text, fill='red', font=font)
        
        return new_img
        
    except Exception as e:
        print(f"Error drawing molecule {name}: {e}")
        return None

def create_comparison_figure():
    """创建分子结构对比图"""
    # 按相似性排序
    data_sorted = data.sort_values('Tanimoto_Sim_To_Halicin', ascending=False)
    
    # 计算网格布局 (3列)
    n_compounds = len(data_sorted) + 1  # +1 for Halicin
    n_cols = 3
    n_rows = (n_compounds + n_cols - 1) // n_cols
    
    # 创建图形
    fig_width = 15
    fig_height = n_rows * 4
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(fig_width, fig_height))
    
    # 确保axes是2D数组
    if n_rows == 1:
        axes = axes.reshape(1, -1)
    if n_cols == 1:
        axes = axes.reshape(-1, 1)
    
    # 首先绘制Halicin参考结构
    halicin_img = draw_molecule_with_info(halicin_smiles, "Halicin", "Halicin (参考)", None)
    if halicin_img:
        axes[0, 0].imshow(halicin_img)
        axes[0, 0].set_title("参考化合物 Halicin", fontsize=12, fontweight='bold', color='blue')
    axes[0, 0].axis('off')
    
    # 绘制其他化合物
    for idx, (_, row) in enumerate(data_sorted.iterrows()):
        plot_idx = idx + 1  # +1 because Halicin is at index 0
        row_idx = plot_idx // n_cols
        col_idx = plot_idx % n_cols
        
        if row_idx < n_rows and col_idx < n_cols:
            mol_img = draw_molecule_with_info(
                row['SMILES'], 
                row['Name'], 
                row['Chinese_Name'], 
                row['Tanimoto_Sim_To_Halicin']
            )
            
            if mol_img:
                axes[row_idx, col_idx].imshow(mol_img)
                
                # 根据相似性设置标题颜色
                similarity = row['Tanimoto_Sim_To_Halicin']
                if similarity > 0.3:
                    color = 'red'
                elif similarity > 0.25:
                    color = 'orange'
                else:
                    color = 'green'
                
                title = f"{row['Chinese_Name']}\n相似性: {similarity:.3f}"
                axes[row_idx, col_idx].set_title(title, fontsize=10, color=color)
            
            axes[row_idx, col_idx].axis('off')
    
    # 隐藏空的子图
    for idx in range(n_compounds, n_rows * n_cols):
        row_idx = idx // n_cols
        col_idx = idx % n_cols
        if row_idx < n_rows and col_idx < n_cols:
            axes[row_idx, col_idx].axis('off')
    
    # 设置总标题
    fig.suptitle('Halicin相似化合物分子结构对比图\nMolecular Structure Comparison with Halicin', 
                 fontsize=16, fontweight='bold', y=0.98)
    
    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    
    # 保存图形
    plt.savefig('Halicin_Molecular_Structures.png', dpi=300, bbox_inches='tight')
    plt.savefig('Halicin_Molecular_Structures.pdf', bbox_inches='tight')
    plt.close()  # 关闭图形而不显示
    
    print("分子结构对比图已保存为:")
    print("- Halicin_Molecular_Structures.png")
    print("- Halicin_Molecular_Structures.pdf")

def create_similarity_ranking_chart():
    """创建相似性排名图表"""
    data_sorted = data.sort_values('Tanimoto_Sim_To_Halicin', ascending=True)
    
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 创建颜色映射
    colors = plt.cm.RdYlBu_r(np.linspace(0.2, 0.8, len(data_sorted)))
    
    # 绘制水平条形图
    bars = ax.barh(range(len(data_sorted)), data_sorted['Tanimoto_Sim_To_Halicin'], 
                   color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)
    
    # 添加数值标签
    for i, (idx, row) in enumerate(data_sorted.iterrows()):
        ax.text(row['Tanimoto_Sim_To_Halicin'] + 0.005, i, 
                f"{row['Tanimoto_Sim_To_Halicin']:.3f}", 
                va='center', fontsize=10, fontweight='bold')
    
    # 设置y轴标签
    ax.set_yticks(range(len(data_sorted)))
    ax.set_yticklabels([f"{row['Chinese_Name']}\n({row['Name']})" 
                        for _, row in data_sorted.iterrows()], fontsize=10)
    
    # 设置标题和标签
    ax.set_xlabel('Tanimoto相似性指数 (Tanimoto Similarity Index)', fontsize=12)
    ax.set_title('化合物与Halicin的结构相似性排名\nStructural Similarity Ranking to Halicin', 
                 fontsize=14, fontweight='bold', pad=20)
    
    # 设置x轴范围
    ax.set_xlim(0, max(data_sorted['Tanimoto_Sim_To_Halicin']) * 1.15)
    
    # 添加网格
    ax.grid(axis='x', alpha=0.3, linestyle='--')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图形
    plt.savefig('Halicin_Similarity_Ranking.png', dpi=300, bbox_inches='tight')
    plt.savefig('Halicin_Similarity_Ranking.pdf', bbox_inches='tight')
    plt.close()  # 关闭图形而不显示
    
    print("相似性排名图已保存为:")
    print("- Halicin_Similarity_Ranking.png")
    print("- Halicin_Similarity_Ranking.pdf")

if __name__ == "__main__":
    print("=== 开始生成Halicin相似性分析图表 ===")
    
    # 检查RDKit是否可用
    try:
        from rdkit import Chem
        print("RDKit已加载，开始生成分子结构图...")
        create_comparison_figure()
    except ImportError:
        print("警告: RDKit未安装，跳过分子结构图生成")
    
    # 生成相似性排名图
    print("生成相似性排名图...")
    create_similarity_ranking_chart()
    
    print("=== 所有图表生成完成 ===")
