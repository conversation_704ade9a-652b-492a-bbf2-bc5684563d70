---
type: "manual"
---

# 数据分析与绘图完整性规则

## 核心原则：数据真实性第一

### 🚫 绝对禁止
- **编造任何数据**：永远不得创建、模拟或假设任何数据点
- **使用示例数据替代真实数据**：除非明确标注为演示目的
- **隐瞒数据缺失或问题**：必须如实报告数据状况
- **跳过数据验证步骤**：每次分析前必须验证数据完整性

### 📋 强制性数据审查流程

#### 分析前审查（Pre-Analysis Review）
1. **数据源确认**
   - 验证所有输入文件存在且可访问
   - 检查数据文件的修改时间和大小
   - 确认数据格式和编码正确

2. **数据完整性检查**
   - 统计缺失值数量和分布
   - 识别异常值和离群点
   - 验证数据类型和范围合理性
   - 检查重复记录

3. **数据质量评估**
   - 计算数据完整度百分比
   - 评估数据一致性
   - 检查时间序列的连续性（如适用）

#### 分析中监控（During-Analysis Monitoring）
1. **实时数据验证**
   - 每个处理步骤后检查数据维度
   - 验证转换操作的合理性
   - 监控数据分布变化

2. **中间结果验证**
   - 检查计算结果的合理性
   - 验证统计指标的有效性
   - 确认可视化反映真实数据

#### 分析后验证（Post-Analysis Validation）
1. **结果一致性检查**
   - 交叉验证不同方法的结果
   - 检查结果与预期范围的一致性
   - 验证可视化与数值结果的对应关系

2. **可重现性验证**
   - 确保分析步骤可重复执行
   - 验证随机种子设置（如适用）
   - 检查输出文件的完整性

### 🔍 数据问题识别与处理

#### 发现问题时的处理流程
1. **立即停止分析**：发现数据问题时立即暂停
2. **详细记录问题**：记录问题类型、位置、影响范围
3. **主动沟通**：向用户详细说明发现的问题
4. **提供解决方案**：建议可行的数据清理或替代方案
5. **等待确认**：获得用户明确指示后再继续

#### 常见数据问题清单
- 缺失值超过合理阈值（>5%）
- 数据类型不匹配
- 数值超出合理范围
- 时间戳格式错误
- 编码问题导致的乱码
- 文件损坏或不完整
- 数据结构与预期不符

### 📊 绘图质量标准

#### 数据可视化原则
1. **数据驱动绘图**
   - 所有图表必须基于实际数据
   - 不得为了美观而修改数据点
   - 保持数据的原始分布特征

2. **透明度要求**
   - 明确标注数据来源
   - 显示样本数量和统计信息
   - 标注任何数据转换或筛选操作

3. **质量控制**
   - 验证图表与源数据的一致性
   - 检查坐标轴范围和标签正确性
   - 确保图例和标注准确无误

#### 输出文件标准
- 保存高分辨率矢量格式（PDF优先）
- 包含数据生成时间戳
- 文件名反映内容和版本信息
- 同时保存原始数据和处理后数据

### 🗣️ 沟通与报告要求

#### 问题报告格式
```
⚠️ 数据问题发现

文件：[文件名]
问题类型：[具体问题]
影响范围：[受影响的分析部分]
建议方案：[可选的解决方案]
需要决策：[需要用户确认的事项]
```

#### 分析报告要求
1. **数据说明部分**
   - 数据来源和收集时间
   - 样本数量和完整性
   - 数据预处理步骤

2. **方法说明部分**
   - 分析方法选择理由
   - 参数设置和假设条件
   - 质量控制措施

3. **结果解释部分**
   - 结果的统计意义
   - 局限性和不确定性
   - 与预期的对比分析

### 🔄 持续改进机制

#### 经验总结
- 记录每次分析中遇到的数据问题
- 建立常见问题的解决方案库
- 优化数据验证流程

#### 质量提升
- 定期审查分析流程的有效性
- 更新数据质量检查标准
- 改进自动化验证工具

### 🎯 项目特定要求

基于当前化学空间分析项目的特点，额外强调：

1. **化学数据验证**
   - 验证SMILES字符串的有效性
   - 检查分子量和性质的合理范围
   - 确认活性数据的单位和尺度正确

2. **数据集整合**
   - 确保不同数据源的格式统一
   - 验证合并数据时的一致性
   - 处理重复化合物的策略透明

3. **绘图特殊要求**
   - 化学空间图需反映真实的分子分布
   - PCA/t-SNE等降维结果必须可重现
   - 活性热图的颜色映射要科学合理

---

**记住：数据的真实性和完整性是所有分析工作的基础。宁可因为数据问题暂停分析，也不能基于有问题的数据得出错误结论。**
