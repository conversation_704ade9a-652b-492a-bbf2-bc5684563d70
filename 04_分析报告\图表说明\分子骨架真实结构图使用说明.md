# 分子骨架真实结构图使用说明

**作者**: ZK  
**邮箱**: <EMAIL>  
**日期**: 2025-07-14  
**版本**: v2.0 (优化版)

## 📋 更新说明

### v2.0 优化内容
- ✅ **移除SMILES显示**：界面更加简洁，突出分子结构本身
- ✅ **增大文字尺寸**：Scaffold ID字体从18pt增加到22pt，提高可读性
- ✅ **减小分子图片**：从400×400减小到320×320像素，布局更紧凑
- ✅ **优化间距**：调整边距和间距，整体布局更协调
- ✅ **增加JPEG格式**：提供PNG、PDF、JPEG三种格式选择

## 🚀 快速开始

### 1. 环境检查
```bash
python "02_分析脚本/Python脚本/环境配置检查.py"
```

### 2. 生成图表
```bash
python "02_分析脚本/Python脚本/分子骨架真实结构图生成器.py"
```

### 3. 查看结果
生成的文件位于：`03_图表输出/论文级图表/`
- `分子骨架真实结构图_RDKit_PIL.png` - 高分辨率PNG (推荐用于论文)
- `分子骨架真实结构图_RDKit_PIL.pdf` - 矢量PDF (可无损缩放)
- `分子骨架真实结构图_RDKit_PIL.jpg` - JPEG格式 (文件较小)

## 📊 图表特点

### 视觉设计
- **4×5网格布局**：清晰展示20个分子骨架
- **真实2D结构**：使用RDKit生成标准化学结构图
- **中国风配色**：朱红色Scaffold ID，靛蓝色边框
- **Nature风格**：符合顶级期刊图表标准

### 技术规格
- **分辨率**：300 DPI高分辨率输出
- **分子图像**：320×320像素，清晰展示结构细节
- **字体大小**：标题28pt，Scaffold ID 22pt，说明14pt
- **总尺寸**：约1800×2200像素（适合A4纸张）

## 🔧 环境要求

### 必需依赖
```
Python 3.8+
├── RDKit (>=2023.3.1)     # 分子处理
└── Pillow (>=9.0.0)       # 图像处理
```

### 安装方法
```bash
# 方法1: 使用conda（推荐）
conda create -n molecular python=3.9
conda activate molecular
conda install -c conda-forge rdkit
pip install Pillow

# 方法2: 使用pip
pip install rdkit Pillow
```

## 📁 文件结构

```
02_分析脚本/Python脚本/
├── 分子骨架真实结构图生成器.py      # 主程序
├── 环境配置检查.py                   # 环境诊断工具
└── requirements_stable.txt           # 依赖列表

01_原始数据/分子骨架数据/
└── scaffold_smiles_data.csv          # 输入数据

03_图表输出/论文级图表/
├── 分子骨架真实结构图_RDKit_PIL.png # 输出图片
├── 分子骨架真实结构图_RDKit_PIL.pdf # 输出PDF
└── 分子骨架真实结构图_RDKit_PIL.jpg # 输出JPEG
```

## 🎨 自定义选项

### 修改配色方案
在脚本中找到 `setup_chinese_academic_style()` 函数：
```python
colors = {
    'primary': '#C0392B',      # Scaffold ID颜色
    'secondary': '#2980B9',    # 边框颜色
    'text': '#34495E',         # 文本颜色
    'background': '#FDFEFE',   # 背景颜色
}
```

### 调整布局参数
在 `create_grid_layout()` 函数中：
```python
mol_size = 320      # 分子图像大小
text_height = 80    # 文本区域高度
margin = 60         # 边距
spacing = 40        # 分子间距
```

### 修改字体大小
```python
title_font = get_font(font_options, 28)    # 标题字体
id_font = get_font(font_options, 22)       # ID字体
caption_font = get_font(font_options, 14)  # 说明字体
```

## ⚠️ 常见问题

### Q1: RDKit安装失败
**解决方案**：使用conda安装更稳定
```bash
conda install -c conda-forge rdkit
```

### Q2: 字体显示异常
**解决方案**：脚本会自动使用系统默认字体，无需额外配置

### Q3: 图片质量不够
**解决方案**：
- PNG格式已设置300 DPI，适合学术发表
- PDF格式为矢量图，可无损缩放
- 如需更高质量，可修改 `mol_size` 参数

### Q4: numpy版本冲突
**解决方案**：
```bash
pip install 'numpy<2.0.0'
```

## 📈 使用建议

### 学术发表
- **推荐格式**：PNG (300 DPI) 用于论文插图
- **备选格式**：PDF 用于需要缩放的场合
- **文件大小**：PNG约2-3MB，PDF约1-2MB

### 演示展示
- **推荐格式**：JPEG 文件较小，加载快速
- **显示设备**：适合投影仪和屏幕显示
- **打印输出**：建议使用PNG或PDF格式

### 进一步编辑
- **矢量编辑**：使用PDF格式导入Adobe Illustrator
- **位图编辑**：使用PNG格式导入Photoshop
- **文档插入**：Word/PowerPoint推荐PNG格式

## 🔄 版本历史

- **v2.0** (2025-07-14): 优化布局，移除SMILES，增大字体
- **v1.0** (2025-07-14): 初始版本，基础功能实现

## 📞 技术支持

如遇到问题，请：
1. 首先运行环境检查工具
2. 查看本说明文档的常见问题部分
3. 联系作者：<EMAIL>

---

**注意**：本工具专为学术研究设计，生成的图表符合Nature等顶级期刊的发表标准。
