<?xml version='1.0' encoding='UTF-8' ?>
<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='283.46pt' height='226.77pt' viewBox='0 0 283.46 226.77'>
<g class='svglite'>
<defs>
  <style type='text/css'><![CDATA[
    .svglite line, .svglite polyline, .svglite polygon, .svglite path, .svglite rect, .svglite circle {
      fill: none;
      stroke: #000000;
      stroke-linecap: round;
      stroke-linejoin: round;
      stroke-miterlimit: 10.00;
    }
    .svglite text {
      white-space: pre;
    }
    .svglite g.glyphgroup path {
      fill: inherit;
      stroke: none;
    }
  ]]></style>
</defs>
<rect width='100%' height='100%' style='stroke: none; fill: #FFFFFF;'/>
<defs>
  <clipPath id='cpMC4wMHwyODMuNDZ8MC4wMHwyMjYuNzc='>
    <rect x='0.00' y='0.00' width='283.46' height='226.77' />
  </clipPath>
</defs>
<g clip-path='url(#cpMC4wMHwyODMuNDZ8MC4wMHwyMjYuNzc=)'>
<rect x='0.00' y='0.000000000000028' width='283.46' height='226.77' style='stroke-width: 0.68; stroke: #FFFFFF; fill: #FFFFFF;' />
</g>
<defs>
  <clipPath id='cpNjAuNTN8MjA3Ljg5fDM0Ljc1fDIwMi42Mw=='>
    <rect x='60.53' y='34.75' width='147.36' height='167.88' />
  </clipPath>
</defs>
<g clip-path='url(#cpNjAuNTN8MjA3Ljg5fDM0Ljc1fDIwMi42Mw==)'>
<rect x='60.53' y='34.75' width='147.36' height='167.88' style='stroke-width: 0.68; stroke: none; fill: #FFFFFF;' />
<rect x='67.23' y='37.08' width='133.96' height='23.32' style='stroke-width: 1.07; stroke: #FFFFFF; stroke-linecap: butt; stroke-linejoin: miter; fill: #F0F921;' />
<rect x='67.23' y='60.40' width='133.96' height='23.32' style='stroke-width: 1.07; stroke: #FFFFFF; stroke-linecap: butt; stroke-linejoin: miter; fill: #C9467C;' />
<rect x='67.23' y='83.71' width='133.96' height='23.32' style='stroke-width: 1.07; stroke: #FFFFFF; stroke-linecap: butt; stroke-linejoin: miter; fill: #7706A4;' />
<rect x='67.23' y='107.03' width='133.96' height='23.32' style='stroke-width: 1.07; stroke: #FFFFFF; stroke-linecap: butt; stroke-linejoin: miter; fill: #3E0595;' />
<rect x='67.23' y='130.35' width='133.96' height='23.32' style='stroke-width: 1.07; stroke: #FFFFFF; stroke-linecap: butt; stroke-linejoin: miter; fill: #370692;' />
<rect x='67.23' y='153.67' width='133.96' height='23.32' style='stroke-width: 1.07; stroke: #FFFFFF; stroke-linecap: butt; stroke-linejoin: miter; fill: #1F078B;' />
<rect x='67.23' y='176.98' width='133.96' height='23.32' style='stroke-width: 1.07; stroke: #FFFFFF; stroke-linecap: butt; stroke-linejoin: miter; fill: #0D0887;' />
<text x='134.21' y='51.28' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #FFFFFF; font-family: "Arial";' textLength='17.78px' lengthAdjust='spacingAndGlyphs'>0.370</text>
<text x='134.21' y='74.60' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #FFFFFF; font-family: "Arial";' textLength='17.78px' lengthAdjust='spacingAndGlyphs'>0.283</text>
<text x='134.21' y='97.92' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #FFFFFF; font-family: "Arial";' textLength='17.78px' lengthAdjust='spacingAndGlyphs'>0.239</text>
<text x='134.21' y='121.23' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #FFFFFF; font-family: "Arial";' textLength='17.78px' lengthAdjust='spacingAndGlyphs'>0.214</text>
<text x='134.21' y='144.55' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #FFFFFF; font-family: "Arial";' textLength='17.78px' lengthAdjust='spacingAndGlyphs'>0.212</text>
<text x='134.21' y='167.87' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #FFFFFF; font-family: "Arial";' textLength='17.78px' lengthAdjust='spacingAndGlyphs'>0.204</text>
<text x='134.21' y='191.19' text-anchor='middle' style='font-size: 7.11px; font-weight: bold;fill: #FFFFFF; font-family: "Arial";' textLength='17.78px' lengthAdjust='spacingAndGlyphs'>0.200</text>
<rect x='60.53' y='34.75' width='147.36' height='167.88' style='stroke-width: 1.07;' />
</g>
<g clip-path='url(#cpMC4wMHwyODMuNDZ8MC4wMHwyMjYuNzc=)'>
<text x='57.40' y='191.15' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='28.00px' lengthAdjust='spacingAndGlyphs'>氨基噻唑</text>
<text x='57.40' y='167.83' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='28.00px' lengthAdjust='spacingAndGlyphs'>塞克硝唑</text>
<text x='57.40' y='144.51' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='21.00px' lengthAdjust='spacingAndGlyphs'>甲硝唑</text>
<text x='57.40' y='121.20' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='21.00px' lengthAdjust='spacingAndGlyphs'>罗硝唑</text>
<text x='57.40' y='97.88' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='35.00px' lengthAdjust='spacingAndGlyphs'>二甲硝咪唑</text>
<text x='57.40' y='74.56' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='28.00px' lengthAdjust='spacingAndGlyphs'>硝唑尼特</text>
<text x='57.40' y='51.24' text-anchor='end' style='font-size: 7.00px; font-family: "Arial";' textLength='21.00px' lengthAdjust='spacingAndGlyphs'>硝硫胺</text>
<polyline points='58.79,188.64 60.53,188.64 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='58.79,165.32 60.53,165.32 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='58.79,142.01 60.53,142.01 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='58.79,118.69 60.53,118.69 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='58.79,95.37 60.53,95.37 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='58.79,72.06 60.53,72.06 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<polyline points='58.79,48.74 60.53,48.74 ' style='stroke-width: 1.07; stroke-linecap: butt;' />
<text transform='translate(19.18,118.69) rotate(-90)' text-anchor='middle' style='font-size: 7.00px; font-family: "Arial";' textLength='96.44px' lengthAdjust='spacingAndGlyphs'>化合物名称 (Compound Name)</text>
<rect x='214.86' y='63.24' width='54.43' height='110.90' style='stroke-width: 0.68; stroke: none; fill: #FFFFFF;' />
<text x='218.35' y='72.47' style='font-size: 7.00px; font-family: "Arial";' textLength='35.00px' lengthAdjust='spacingAndGlyphs'>相似性指数</text>
<text x='218.35' y='80.03' style='font-size: 7.00px; font-family: "Arial";' textLength='47.45px' lengthAdjust='spacingAndGlyphs'>Similarity Index</text>
<image width='17.28' height='86.40' x='218.35' y='84.25' preserveAspectRatio='none' xlink:href='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAEsCAYAAAACUNnVAAAA4klEQVQ4jY2QS5LDMAhEH9wk97/cbFMV07NwjAWWYu+A/kr8vV9yZLgF7BMCt7DJCk4YbkpyyizRXVHXMaMB1b651FZD2mnV/BKwoV/RNt4lbaIYAMMly8m/BpKtKOdPzgCOKVbA6ta06uHN4HzMb/LSdDLFrUt7grqV4eJqcNFWP4rVk4w1mUc1brRxT1nKoAB9bSW5BXKNJ6bHGvpVI5pilpFAtDS1LmfaQnGu0RSAC+EyHQ1QAeCLxnCrZON6q+SolP1WM8Imzs2v1phRovpFTYuUjWtOBr7lbUP4BvgH8Q/tz8IBGi+44gAAAABJRU5ErkJggg=='/>
<polyline points='232.18,170.51 235.63,170.51 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='232.18,150.20 235.63,150.20 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='232.18,129.88 235.63,129.88 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='232.18,109.57 235.63,109.57 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='232.18,89.25 235.63,89.25 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='221.81,170.51 218.35,170.51 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='221.81,150.20 218.35,150.20 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='221.81,129.88 218.35,129.88 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='221.81,109.57 218.35,109.57 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<polyline points='221.81,89.25 218.35,89.25 ' style='stroke-width: 0.38; stroke: #FFFFFF; stroke-linecap: butt;' />
<text x='239.12' y='172.51' style='font-size: 5.60px; font-family: "Arial";' textLength='10.88px' lengthAdjust='spacingAndGlyphs'>0.20</text>
<text x='239.12' y='152.20' style='font-size: 5.60px; font-family: "Arial";' textLength='10.88px' lengthAdjust='spacingAndGlyphs'>0.24</text>
<text x='239.12' y='131.88' style='font-size: 5.60px; font-family: "Arial";' textLength='10.88px' lengthAdjust='spacingAndGlyphs'>0.28</text>
<text x='239.12' y='111.57' style='font-size: 5.60px; font-family: "Arial";' textLength='10.88px' lengthAdjust='spacingAndGlyphs'>0.32</text>
<text x='239.12' y='91.26' style='font-size: 5.60px; font-family: "Arial";' textLength='10.88px' lengthAdjust='spacingAndGlyphs'>0.36</text>
<text x='60.53' y='29.79' style='font-size: 7.00px; font-family: "Arial";' textLength='56.03px' lengthAdjust='spacingAndGlyphs'>Halicin相似性热图</text>
<text x='134.21' y='19.68' text-anchor='middle' style='font-size: 7.70px; font-family: "Arial";' textLength='89.73px' lengthAdjust='spacingAndGlyphs'>Halicin Similarity Heatmap</text>
</g>
</g>
</svg>
