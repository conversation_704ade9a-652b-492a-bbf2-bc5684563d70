# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-11
# 描述: 化学数据集质量分析脚本 - 符合Nature期刊标准

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
np.random.seed(42)

# Nature风格配色方案 (<PERSON>配色)
wong_colors = ['#000000', '#E69F00', '#56B4E9', '#009E73', 
               '#F0E442', '#0072B2', '#D55E00', '#CC79A7']

# 设置matplotlib参数
plt.rcParams['font.family'] = 'Arial'
plt.rcParams['font.size'] = 7
plt.rcParams['axes.linewidth'] = 0.5
plt.rcParams['xtick.major.width'] = 0.5
plt.rcParams['ytick.major.width'] = 0.5

def load_datasets():
    """加载数据集"""
    print("正在加载数据集...")
    
    try:
        # 加载第一个数据集
        dataset1 = pd.read_csv("cleaned_data-12-9-no-duplicate.csv")
        print(f"数据集1加载完成: {dataset1.shape[0]}行, {dataset1.shape[1]}列")
        
        # 加载第二个数据集
        dataset2 = pd.read_csv("filtered_ecoli_9000-nosalt.csv")
        print(f"数据集2加载完成: {dataset2.shape[0]}行, {dataset2.shape[1]}列")
        
        return dataset1, dataset2
    except Exception as e:
        print(f"数据加载错误: {e}")
        return None, None

def basic_info_analysis(data, dataset_name):
    """基本信息分析"""
    print(f"\n=== {dataset_name} 基本信息分析 ===")
    
    # 基本统计
    print(f"数据维度: {data.shape}")
    print(f"列名: {', '.join(data.columns)}")
    
    # 数据类型
    print("\n数据类型:")
    print(data.dtypes)
    
    # 缺失值统计
    missing_counts = data.isnull().sum()
    missing_percent = (missing_counts / len(data) * 100).round(2)
    
    print("\n缺失值统计:")
    missing_summary = pd.DataFrame({
        'Column': missing_counts.index,
        'Missing_Count': missing_counts.values,
        'Missing_Percent': missing_percent.values
    })
    print(missing_summary)
    
    return missing_summary

def smiles_quality_check(smiles_series, dataset_name):
    """SMILES分子结构质量检查"""
    print(f"\n=== {dataset_name} SMILES质量检查 ===")
    
    # 基本统计
    total_smiles = len(smiles_series)
    empty_smiles = smiles_series.isnull().sum() + (smiles_series == "").sum()
    valid_smiles = total_smiles - empty_smiles
    
    print(f"总SMILES数量: {total_smiles}")
    print(f"空/缺失SMILES: {empty_smiles}")
    print(f"有效SMILES: {valid_smiles}")
    
    # SMILES长度分析
    valid_smiles_data = smiles_series.dropna()
    valid_smiles_data = valid_smiles_data[valid_smiles_data != ""]
    smiles_lengths = valid_smiles_data.str.len()
    
    print(f"\nSMILES长度统计:")
    print(f"最小长度: {smiles_lengths.min()}")
    print(f"最大长度: {smiles_lengths.max()}")
    print(f"平均长度: {smiles_lengths.mean():.2f}")
    print(f"中位数长度: {smiles_lengths.median()}")
    
    # 特殊字符统计
    special_chars = ['@', '[', ']', '(', ')', '=', '#', '+', '-']
    char_counts = {}
    for char in special_chars:
        char_counts[char] = valid_smiles_data.str.count(f'\\{char}').sum()
    
    print(f"\n特殊字符统计:")
    for char, count in char_counts.items():
        print(f"{char}: {count}")
    
    return {
        'total': total_smiles,
        'valid': valid_smiles,
        'empty': empty_smiles,
        'lengths': smiles_lengths,
        'char_counts': char_counts
    }

def activity_analysis(activity_series, dataset_name):
    """活性数据分析"""
    print(f"\n=== {dataset_name} 活性数据分析 ===")
    
    # 基本统计
    activity_counts = activity_series.value_counts(dropna=False)
    print("活性分布:")
    print(activity_counts)
    
    # 计算比例
    activity_prop = (activity_counts / len(activity_series) * 100).round(2)
    print(f"\n活性比例(%):")
    print(activity_prop)
    
    return {
        'counts': activity_counts,
        'proportions': activity_prop
    }

def create_quality_plots(results):
    """创建数据质量图表"""
    print("\n正在生成数据质量图表...")
    
    # 创建图表
    fig, axes = plt.subplots(2, 3, figsize=(183/25.4, 120/25.4))  # Nature双栏宽度
    fig.suptitle('Chemical Datasets Quality Analysis', fontsize=8, y=0.95)
    
    # 图1: 数据集大小对比
    datasets = ['Dataset 1\n(cleaned)', 'Dataset 2\n(filtered_ecoli)']
    sizes = [results['dataset1'].shape[0], results['dataset2'].shape[0]]
    
    axes[0,0].bar(datasets, sizes, color=wong_colors[:2])
    axes[0,0].set_title('Dataset Sizes', fontsize=7)
    axes[0,0].set_ylabel('Number of Records', fontsize=7)
    axes[0,0].tick_params(labelsize=6)
    
    # 图2: 活性分布 - 数据集1
    activity1 = results['activity1']['counts']
    axes[0,1].pie(activity1.values, labels=[f'Class {i}' for i in activity1.index], 
                  colors=wong_colors[:len(activity1)], autopct='%1.1f%%')
    axes[0,1].set_title('Dataset 1 Activity Distribution', fontsize=7)
    
    # 图3: 活性分布 - 数据集2
    activity2 = results['activity2']['counts']
    axes[0,2].pie(activity2.values, labels=[f'Class {i}' for i in activity2.index], 
                  colors=wong_colors[:len(activity2)], autopct='%1.1f%%')
    axes[0,2].set_title('Dataset 2 Activity Distribution', fontsize=7)
    
    # 图4: SMILES长度分布 - 数据集1
    axes[1,0].hist(results['smiles1_quality']['lengths'], bins=30, 
                   color=wong_colors[1], alpha=0.7, edgecolor='black', linewidth=0.5)
    axes[1,0].set_title('Dataset 1 SMILES Length Distribution', fontsize=7)
    axes[1,0].set_xlabel('SMILES Length', fontsize=7)
    axes[1,0].set_ylabel('Frequency', fontsize=7)
    axes[1,0].tick_params(labelsize=6)
    
    # 图5: SMILES长度分布 - 数据集2
    axes[1,1].hist(results['smiles2_quality']['lengths'], bins=30, 
                   color=wong_colors[2], alpha=0.7, edgecolor='black', linewidth=0.5)
    axes[1,1].set_title('Dataset 2 SMILES Length Distribution', fontsize=7)
    axes[1,1].set_xlabel('SMILES Length', fontsize=7)
    axes[1,1].set_ylabel('Frequency', fontsize=7)
    axes[1,1].tick_params(labelsize=6)
    
    # 图6: 缺失值对比
    missing_data = {
        'Dataset 1': results['missing1']['Missing_Percent'].sum(),
        'Dataset 2': results['missing2']['Missing_Percent'].sum()
    }
    axes[1,2].bar(missing_data.keys(), missing_data.values(), color=wong_colors[3:5])
    axes[1,2].set_title('Missing Data Percentage', fontsize=7)
    axes[1,2].set_ylabel('Missing Percentage (%)', fontsize=7)
    axes[1,2].tick_params(labelsize=6)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('data_quality_analysis.pdf', dpi=300, bbox_inches='tight')
    plt.savefig('data_quality_analysis.png', dpi=300, bbox_inches='tight')
    print("图表已保存: data_quality_analysis.pdf 和 data_quality_analysis.png")
    
    plt.show()

def main_analysis():
    """主分析函数"""
    print("开始数据质量分析...")
    print("分析时间:", pd.Timestamp.now())
    
    # 加载数据
    dataset1, dataset2 = load_datasets()
    if dataset1 is None or dataset2 is None:
        return None
    
    # 数据集1分析
    print("\n" + "="*50)
    print("数据集1: cleaned_data-12-9-no-duplicate.csv")
    print("="*50)
    
    missing1 = basic_info_analysis(dataset1, "数据集1")
    smiles1_quality = smiles_quality_check(dataset1['Smiles'], "数据集1")
    activity1_analysis = activity_analysis(dataset1['Activity'], "数据集1")
    
    # 数据集2分析
    print("\n" + "="*50)
    print("数据集2: filtered_ecoli_9000-nosalt.csv")
    print("="*50)
    
    missing2 = basic_info_analysis(dataset2, "数据集2")
    smiles2_quality = smiles_quality_check(dataset2['Smiles'], "数据集2")
    activity2_analysis = activity_analysis(dataset2['Activity'], "数据集2")
    
    # 返回分析结果
    results = {
        'dataset1': dataset1,
        'dataset2': dataset2,
        'missing1': missing1,
        'missing2': missing2,
        'smiles1_quality': smiles1_quality,
        'smiles2_quality': smiles2_quality,
        'activity1': activity1_analysis,
        'activity2': activity2_analysis
    }
    
    # 生成图表
    create_quality_plots(results)
    
    return results

if __name__ == "__main__":
    print("数据质量分析开始")
    print("="*60)
    results = main_analysis()
    if results:
        print("\n数据质量分析完成")
        print("="*60)
    else:
        print("分析失败")
