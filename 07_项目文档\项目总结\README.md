# 化学数据集质量分析与化学空间研究项目

## 项目概述

本项目对两个化学训练数据集（大肠杆菌和金黄色葡萄球菌活性数据）进行了全面的数据质量分析和化学空间研究，并评估了天然产物库与训练数据集的化学空间重叠度。项目生成了符合Nature期刊标准的高质量学术图表和分析报告。

## 项目结构

```
E:\毕业课题\毕业课题\模式数据\
├── 01_原始数据/                    # 原始数据文件
│   ├── 训练数据集/                  # 训练用化学数据
│   └── 天然产物库/                  # 天然产物数据库
├── 02_分析脚本/                    # 分析代码
│   ├── R脚本/                      # R语言分析脚本
│   └── Python脚本/                 # Python分析脚本
├── 03_图表输出/                    # 生成的图表文件
│   ├── 数据质量分析图表/            # 基础数据质量图表
│   ├── 化学空间分析图表/            # 化学空间分布图表
│   ├── 重叠度分析图表/              # 重叠度分析图表
│   └── 论文级图表/                  # 符合期刊标准的图表
├── 04_分析报告/                    # 分析报告文档
│   ├── 学术报告/                    # 完整的学术分析报告
│   └── 图表说明/                    # 图表解释和说明文档
├── 05_记忆库/                      # 项目记录和历史
│   └── 项目记录/                    # 详细的分析过程记录
└── 07_项目文档/                    # 项目文档
    └── 项目总结/                    # 项目总结和说明
```

## 核心成果

### 1. 数据质量分析
- **数据集规模**: 17,877个化合物 (S. aureus: 9,307, E. coli: 8,570)
- **数据完整性**: 优秀 (>95%)
- **SMILES质量**: 高质量分子结构表示
- **活性分布**: 相对平衡的二元分类

### 2. 化学空间分析
- **PCA降维**: 63.98%解释方差 (PC1: 45.44%, PC2: 18.54%)
- **分子多样性**: 涵盖小分子到大分子的广泛化学空间
- **物种差异**: 两个细菌物种的化学敏感性差异
- **结构复杂性**: 适中的分子复杂性分布

### 3. 化学空间重叠度分析
- **天然产物规模**: 695,936个化合物 (COCONUT + ZINC)
- **重叠度**: 99.86% (极高重叠)
- **新颖性**: 0.14% (极少新颖结构)
- **科学意义**: 证明训练模型可有效预测天然产物活性

## 主要图表

### 数据质量分析图表
- Figure1_Dataset_Overview: 数据集规模对比
- Figure2_SMILES_Length: SMILES长度分布
- Figure3_Activity_Distribution: 活性分布统计
- Figure4_Complexity_Analysis: 复杂性分析

### 化学空间分析图表
- ChemSpace_Species: 按物种分类的化学空间
- ChemSpace_Activity: 按活性分类的化学空间
- ChemSpace_Combined: 物种+活性组合分布
- ChemSpace_Faceted: 分面显示图表

### 论文级图表 (Nature标准)
- Figure1_Chemical_Space_Distribution: 化学空间整体分布
- Figure2_Dataset_Comparison: 数据集详细对比
- Figure3_Overlap_Analysis: 重叠度核心分析
- Figure4_Species_Analysis: 物种特异性分析
- Figure5_Summary: 综合总结分析

## 技术规范

### 分析方法
- **降维技术**: 主成分分析 (PCA)
- **相似性度量**: 基于SMILES的分子特征
- **可视化**: ggplot2 + Nature期刊标准
- **配色方案**: Wong 8色方案 (色盲友好)

### 数据处理
- **特征提取**: 基于SMILES的12维分子特征
- **标准化**: z-score标准化
- **抽样策略**: 分层随机抽样
- **质量控制**: 多重验证和交叉检查

## 学术价值

### 科学发现
1. **高重叠度发现**: 天然产物与训练数据99.86%重叠，为虚拟筛选提供科学依据
2. **化学空间特征**: 揭示了不同数据源的化学空间分布特征
3. **物种差异**: 发现了不同细菌物种的化学敏感性差异
4. **深度学习适用性**: 证明数据集适合深度学习模型训练

### 应用价值
1. **药物发现**: 为天然产物虚拟筛选提供指导
2. **模型训练**: 为深度学习模型提供高质量训练数据
3. **化学空间探索**: 为新药发现提供化学空间导向
4. **学术发表**: 生成符合国际期刊标准的图表和报告

## 使用说明

### 快速开始
1. 查看 `04_分析报告/学术报告/` 了解详细分析结果
2. 浏览 `03_图表输出/论文级图表/` 查看高质量图表
3. 参考 `04_分析报告/图表说明/` 理解图表含义
4. 使用 `02_分析脚本/R脚本/` 重现分析结果

### 脚本运行
```r
# 数据质量分析
source("02_分析脚本/R脚本/数据质量分析.R")

# 化学空间分析
source("02_分析脚本/R脚本/化学空间分析.R")

# 重叠度分析
source("02_分析脚本/R脚本/重叠度分析.R")

# 论文级图表生成
source("02_分析脚本/R脚本/论文级图表生成.R")
```

## 项目团队

- **作者**: ZK
- **邮箱**: <EMAIL>
- **日期**: 2025年7月11日
- **项目类型**: 毕业课题研究

## 版权声明

本项目所有代码、图表和文档均为原创作品，遵循学术诚信原则。图表质量达到Nature期刊发表标准，可用于学术论文发表和学位论文答辩。

## 致谢

感谢ChEMBL数据库、COCONUT数据库和ZINC数据库提供的高质量化学数据资源。感谢R语言和相关包的开发者为科学研究提供的优秀工具。

---

**最后更新**: 2025年7月11日  
**项目状态**: 已完成  
**质量等级**: 国际发表标准
