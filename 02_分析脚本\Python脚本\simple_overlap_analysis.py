# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-11
# 描述: 简化版化学空间重叠度分析

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
np.random.seed(42)

# Wong配色方案
wong_colors = ['#000000', '#E69F00', '#56B4E9', '#009E73', 
               '#F0E442', '#0072B2', '#D55E00', '#CC79A7']

def load_and_sample_data():
    """加载数据并进行抽样"""
    print("正在加载数据集...")
    
    # 加载训练数据集
    try:
        train1 = pd.read_csv("cleaned_data-12-9-no-duplicate.csv")
        train1['Dataset'] = 'S. aureus'
        train1['Type'] = 'Training'
        print(f"训练数据集1加载成功: {len(train1)}个化合物")
    except Exception as e:
        print(f"训练数据集1加载失败: {e}")
        return None
    
    try:
        train2 = pd.read_csv("filtered_ecoli_9000-nosalt.csv")
        train2['Dataset'] = 'E. coli'
        train2['Type'] = 'Training'
        print(f"训练数据集2加载成功: {len(train2)}个化合物")
    except Exception as e:
        print(f"训练数据集2加载失败: {e}")
        return None
    
    # 统一列名
    train1_clean = train1[['Smiles', 'Activity', 'Dataset', 'Type']].copy()
    train2_clean = train2[['Smiles', 'Activity', 'Dataset', 'Type']].copy()
    
    # 加载天然产物数据并抽样
    natural_products = []
    
    try:
        coconut = pd.read_csv("天然产物/clean-coconut_filtered_with_weight_under_500-10-2024.csv")
        # 随机抽样2%
        coconut_sample = coconut.sample(n=min(10000, int(len(coconut) * 0.02)), random_state=42)
        coconut_sample = coconut_sample.rename(columns={'canonical_smiles': 'Smiles'})
        coconut_sample['Dataset'] = 'COCONUT'
        coconut_sample['Type'] = 'Natural Products'
        coconut_sample['Activity'] = -1
        natural_products.append(coconut_sample)
        print(f"COCONUT抽样完成: {len(coconut_sample)}个化合物 (原始: {len(coconut)})")
    except Exception as e:
        print(f"COCONUT数据处理失败: {e}")
    
    try:
        zinc = pd.read_csv("天然产物/merged_zinc_data.csv")
        # 随机抽样5%
        zinc_sample = zinc.sample(n=min(10000, int(len(zinc) * 0.05)), random_state=42)
        zinc_sample = zinc_sample.rename(columns={'smiles': 'Smiles'})
        zinc_sample['Dataset'] = 'ZINC'
        zinc_sample['Type'] = 'Natural Products'
        zinc_sample['Activity'] = -1
        natural_products.append(zinc_sample)
        print(f"ZINC抽样完成: {len(zinc_sample)}个化合物 (原始: {len(zinc)})")
    except Exception as e:
        print(f"ZINC数据处理失败: {e}")
    
    # 合并所有数据
    all_datasets = [train1_clean, train2_clean] + natural_products
    combined_data = pd.concat(all_datasets, ignore_index=True)
    
    # 数据清洗
    combined_data = combined_data.dropna(subset=['Smiles'])
    combined_data = combined_data[combined_data['Smiles'].str.len() > 5]
    
    print(f"最终数据集: {len(combined_data)}个化合物")
    return combined_data

def calculate_features(smiles_series):
    """计算简化分子特征"""
    print("计算分子特征...")
    
    features = pd.DataFrame({
        'Length': smiles_series.str.len(),
        'Carbon': smiles_series.str.count('C'),
        'Nitrogen': smiles_series.str.count('N'),
        'Oxygen': smiles_series.str.count('O'),
        'Sulfur': smiles_series.str.count('S'),
        'Rings': smiles_series.str.count(r'[0-9]'),
        'Aromatic': smiles_series.str.count(r'[cnos]'),
        'DoubleBonds': smiles_series.str.count('='),
        'Stereo': smiles_series.str.count('@'),
        'Branches': smiles_series.str.count(r'\(')
    })
    
    features = features.fillna(0)
    return features

def perform_pca_analysis(data, features):
    """执行PCA分析"""
    print("执行PCA降维分析...")
    
    # 标准化特征
    scaler = StandardScaler()
    features_scaled = scaler.fit_transform(features)
    
    # PCA降维
    pca = PCA(n_components=2)
    pca_coords = pca.fit_transform(features_scaled)
    
    # 计算解释方差
    var_explained = pca.explained_variance_ratio_ * 100
    print(f"PC1解释方差: {var_explained[0]:.2f}%")
    print(f"PC2解释方差: {var_explained[1]:.2f}%")
    print(f"总解释方差: {var_explained.sum():.2f}%")
    
    # 添加PCA坐标
    data['PC1'] = pca_coords[:, 0]
    data['PC2'] = pca_coords[:, 1]
    
    return data, var_explained

def calculate_overlap_metrics(data):
    """计算重叠度指标"""
    print("计算化学空间重叠度...")
    
    training_data = data[data['Type'] == 'Training']
    np_data = data[data['Type'] == 'Natural Products']
    
    if len(np_data) == 0:
        return {'overlap_ratio': 0, 'novelty_ratio': 100}
    
    # 计算训练数据的化学空间边界
    train_pc1_min, train_pc1_max = training_data['PC1'].min(), training_data['PC1'].max()
    train_pc2_min, train_pc2_max = training_data['PC2'].min(), training_data['PC2'].max()
    
    # 扩展边界以包含更多重叠区域
    pc1_margin = (train_pc1_max - train_pc1_min) * 0.1
    pc2_margin = (train_pc2_max - train_pc2_min) * 0.1
    
    # 计算天然产物在扩展训练空间内的比例
    np_in_space = np_data[
        (np_data['PC1'] >= train_pc1_min - pc1_margin) & 
        (np_data['PC1'] <= train_pc1_max + pc1_margin) &
        (np_data['PC2'] >= train_pc2_min - pc2_margin) & 
        (np_data['PC2'] <= train_pc2_max + pc2_margin)
    ]
    
    overlap_ratio = len(np_in_space) / len(np_data) * 100
    novelty_ratio = 100 - overlap_ratio
    
    print(f"化学空间重叠度: {overlap_ratio:.2f}%")
    print(f"新颖性比例: {novelty_ratio:.2f}%")
    
    return {
        'overlap_ratio': overlap_ratio,
        'novelty_ratio': novelty_ratio,
        'overlapping_count': len(np_in_space),
        'total_np_count': len(np_data),
        'training_count': len(training_data)
    }

def create_visualizations(data, var_explained, metrics):
    """创建可视化图表"""
    print("创建可视化图表...")
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 9))
    fig.suptitle('Chemical Space Overlap Analysis: Natural Products vs Training Data', 
                 fontsize=12, y=0.95)
    
    # 轴标签
    pc1_label = f"PC1 ({var_explained[0]:.1f}% variance)"
    pc2_label = f"PC2 ({var_explained[1]:.1f}% variance)"
    
    # 图1: 按类型分类
    for i, (data_type, color) in enumerate(zip(['Training', 'Natural Products'], wong_colors[:2])):
        subset = data[data['Type'] == data_type]
        if len(subset) > 0:
            axes[0,0].scatter(subset['PC1'], subset['PC2'], 
                             c=color, alpha=0.6, s=10, label=data_type)
    
    axes[0,0].set_xlabel(pc1_label)
    axes[0,0].set_ylabel(pc2_label)
    axes[0,0].set_title('Chemical Space by Data Type')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # 图2: 按数据集分类
    datasets = data['Dataset'].unique()
    for i, dataset in enumerate(datasets):
        subset = data[data['Dataset'] == dataset]
        if len(subset) > 0:
            axes[0,1].scatter(subset['PC1'], subset['PC2'], 
                             c=wong_colors[i], alpha=0.6, s=10, label=dataset)
    
    axes[0,1].set_xlabel(pc1_label)
    axes[0,1].set_ylabel(pc2_label)
    axes[0,1].set_title('Chemical Space by Dataset')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    
    # 图3: 分子长度分布
    training_data = data[data['Type'] == 'Training']
    np_data = data[data['Type'] == 'Natural Products']
    
    if len(training_data) > 0:
        axes[1,0].hist(training_data['Length'], bins=30, alpha=0.7, 
                       color=wong_colors[0], label='Training', density=True)
    if len(np_data) > 0:
        axes[1,0].hist(np_data['Length'], bins=30, alpha=0.7, 
                       color=wong_colors[1], label='Natural Products', density=True)
    
    axes[1,0].set_xlabel('SMILES Length')
    axes[1,0].set_ylabel('Density')
    axes[1,0].set_title('Molecular Complexity Distribution')
    axes[1,0].legend()
    axes[1,0].grid(True, alpha=0.3)
    
    # 图4: 重叠度统计
    categories = ['Overlapping\nwith Training', 'Novel\nCompounds']
    values = [metrics['overlap_ratio'], metrics['novelty_ratio']]
    
    bars = axes[1,1].bar(categories, values, color=wong_colors[2:4])
    axes[1,1].set_ylabel('Percentage (%)')
    axes[1,1].set_title('Chemical Space Overlap Statistics')
    axes[1,1].grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        axes[1,1].text(bar.get_x() + bar.get_width()/2., height + 1,
                      f'{value:.1f}%', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('Chemical_Space_Overlap.pdf', dpi=300, bbox_inches='tight')
    plt.savefig('Chemical_Space_Overlap.png', dpi=300, bbox_inches='tight')
    print("图表已保存: Chemical_Space_Overlap.pdf/.png")
    
    plt.show()

def main():
    """主函数"""
    print("开始化学空间重叠度分析")
    print("="*50)
    
    # 加载和抽样数据
    data = load_and_sample_data()
    if data is None:
        print("数据加载失败")
        return
    
    # 计算分子特征
    features = calculate_features(data['Smiles'])
    
    # 执行PCA分析
    analyzed_data, var_explained = perform_pca_analysis(data, features)
    
    # 计算重叠度指标
    metrics = calculate_overlap_metrics(analyzed_data)
    
    # 创建可视化
    create_visualizations(analyzed_data, var_explained, metrics)
    
    # 保存结果
    results_df = pd.DataFrame([metrics])
    results_df.to_csv('Overlap_Analysis_Results.csv', index=False)
    
    # 输出总结
    print("\n" + "="*50)
    print("化学空间重叠度分析完成")
    print("="*50)
    print(f"训练数据化合物: {metrics['training_count']}")
    print(f"天然产物化合物: {metrics['total_np_count']}")
    print(f"化学空间重叠度: {metrics['overlap_ratio']:.2f}%")
    print(f"新颖化合物比例: {metrics['novelty_ratio']:.2f}%")
    print(f"PCA总解释方差: {var_explained.sum():.2f}%")
    print("结果已保存至: Overlap_Analysis_Results.csv")

if __name__ == "__main__":
    main()
