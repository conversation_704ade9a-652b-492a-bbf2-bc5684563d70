# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-14
# 描述: 使用RDKit+PIL生成真实2D分子结构图 - 稳定版本（最小依赖）

import csv
import os
import sys
from io import BytesIO

# 环境检测和导入
def check_and_import_dependencies():
    """检测并导入必要的依赖包"""
    print("=== 环境依赖检测 ===")
    
    # 检测RDKit
    try:
        from rdkit import Chem
        from rdkit.Chem import Draw
        print("✓ RDKit导入成功")
        rdkit_available = True
    except ImportError as e:
        print(f"✗ RDKit导入失败: {e}")
        print("请安装RDKit: conda install -c conda-forge rdkit")
        rdkit_available = False
    
    # 检测PIL
    try:
        from PIL import Image, ImageDraw, ImageFont
        print("✓ PIL导入成功")
        pil_available = True
    except ImportError as e:
        print(f"✗ PIL导入失败: {e}")
        print("请安装Pillow: pip install Pillow")
        pil_available = False
    
    if not (rdkit_available and pil_available):
        print("\n⚠ 缺少必要依赖，程序无法运行")
        return None, None, None, None
    
    from rdkit import Chem
    from rdkit.Chem import Draw
    from PIL import Image, ImageDraw, ImageFont
    
    print("✓ 所有依赖检测通过\n")
    return Chem, Draw, Image, (ImageDraw, ImageFont)

# 全局导入
Chem, Draw, Image, (ImageDraw, ImageFont) = check_and_import_dependencies()
if Chem is None:
    sys.exit(1)

def setup_chinese_academic_style():
    """设置中国风学术配色方案"""
    colors = {
        'primary': '#C0392B',      # 朱红 - 用于强调
        'secondary': '#2980B9',    # 靛蓝 - 用于边框
        'accent1': '#27AE60',      # 石绿 - 用于分类
        'accent2': '#F1C40F',      # 藤黄 - 用于高亮
        'accent3': '#A0522D',      # 赭石 - 用于分组
        'text': '#34495E',         # 青金 - 用于文本
        'background': '#FDFEFE',   # 定窑白 - 背景色
        'molecule': '#000000'      # 黑色 - 分子结构标准色
    }
    
    # 字体设置（尝试多种字体）
    font_options = ['arial.ttf', 'Arial', 'DejaVuSans', 'sans-serif']
    
    return colors, font_options

def load_scaffold_data():
    """加载分子骨架数据"""
    print("=== 分子骨架真实结构图生成器 ===")
    print("加载分子骨架数据...")
    
    data_path = "01_原始数据/分子骨架数据/scaffold_smiles_data.csv"
    
    scaffolds = []
    try:
        with open(data_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                scaffolds.append({
                    'id': row['Scaffold_ID'],
                    'smiles': row['SMILES'],
                    'description': row['Description']
                })
    except FileNotFoundError:
        print(f"✗ 数据文件未找到: {data_path}")
        return []
    except Exception as e:
        print(f"✗ 读取数据文件失败: {str(e)}")
        return []
    
    print(f"数据加载完成，共 {len(scaffolds)} 个分子骨架")
    return scaffolds

def validate_and_create_molecules(scaffolds):
    """验证SMILES并创建分子对象"""
    print("验证SMILES格式并创建分子对象...")
    
    valid_data = []
    
    for scaffold in scaffolds:
        try:
            mol = Chem.MolFromSmiles(scaffold['smiles'])
            if mol is not None:
                valid_data.append({
                    'mol': mol,
                    'id': scaffold['id'],
                    'smiles': scaffold['smiles'],
                    'description': scaffold['description']
                })
                print(f"  ✓ {scaffold['id']}: 分子对象创建成功")
            else:
                print(f"  ✗ {scaffold['id']}: SMILES格式无效")
        except Exception as e:
            print(f"  ✗ {scaffold['id']}: 处理失败 - {str(e)}")
    
    print(f"成功创建 {len(valid_data)} 个分子对象")
    return valid_data

def generate_molecule_image(mol, mol_id, size=(320, 320)):
    """生成单个分子的2D结构图"""
    try:
        # 使用RDKit生成分子图像
        img = Draw.MolToImage(mol, size=size, kekulize=True)
        return img
    except Exception as e:
        print(f"生成分子图像失败 {mol_id}: {str(e)}")
        # 创建错误占位图
        error_img = Image.new('RGB', size, color='white')
        draw = ImageDraw.Draw(error_img)
        draw.text((size[0]//2, size[1]//2), f"Error\n{mol_id}", 
                 fill='red', anchor='mm')
        return error_img

def get_font(font_options, size=20):
    """获取可用字体"""
    for font_name in font_options:
        try:
            if font_name.endswith('.ttf'):
                return ImageFont.truetype(font_name, size)
            else:
                return ImageFont.load_default()
        except:
            continue
    return ImageFont.load_default()

def create_grid_layout(valid_data, colors, font_options):
    """创建4×5网格布局的分子结构图"""
    print("创建4×5网格布局...")

    # 布局参数 - 调整尺寸
    n_cols = 4
    n_rows = 5
    mol_size = 320  # 每个分子图像大小（从400减小到320）
    text_height = 80  # 文本区域高度（从100减小到80，因为不显示SMILES）
    margin = 60  # 边距（稍微增加）
    spacing = 40  # 分子间距（稍微增加）
    
    # 计算画布总尺寸
    canvas_width = margin * 2 + n_cols * mol_size + (n_cols - 1) * spacing
    canvas_height = margin * 2 + n_rows * (mol_size + text_height) + (n_rows - 1) * spacing
    
    # 创建高分辨率画布
    canvas = Image.new('RGB', (canvas_width, canvas_height), color=colors['background'])
    draw = ImageDraw.Draw(canvas)
    
    # 获取字体 - 增大字体尺寸
    title_font = get_font(font_options, 28)  # 从24增加到28
    id_font = get_font(font_options, 22)     # 从18增加到22
    caption_font = get_font(font_options, 14) # 用于底部说明
    
    # 添加总标题
    title_text = "Representative Molecular Scaffolds - Chemical Structure Analysis"
    title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (canvas_width - title_width) // 2
    draw.text((title_x, 20), title_text, fill=colors['text'], font=title_font)
    
    # 绘制分子网格
    for i, data in enumerate(valid_data):
        if i >= n_rows * n_cols:  # 最多显示20个
            break
            
        row = i // n_cols
        col = i % n_cols
        
        # 计算位置
        x = margin + col * (mol_size + spacing)
        y = margin + 60 + row * (mol_size + text_height + spacing)  # 60为标题区域
        
        # 生成并粘贴分子图像
        mol_img = generate_molecule_image(data['mol'], data['id'], (mol_size, mol_size))
        canvas.paste(mol_img, (x, y))
        
        # 添加边框
        draw.rectangle([x-1, y-1, x+mol_size+1, y+mol_size+1], 
                      outline=colors['secondary'], width=2)
        
        # 添加Scaffold ID（居中显示，更大字体）
        id_text = data['id']
        id_bbox = draw.textbbox((0, 0), id_text, font=id_font)
        id_width = id_bbox[2] - id_bbox[0]
        id_x = x + (mol_size - id_width) // 2
        id_y = y + mol_size + 15  # 稍微增加间距
        draw.text((id_x, id_y), id_text, fill=colors['primary'], font=id_font)

        # 不再显示SMILES信息，保持界面简洁
    
    # 添加底部说明（使用更大的字体）
    footer_text = "20 Key Scaffolds from Chemical Database | Generated with RDKit | ZK Research Group"
    footer_bbox = draw.textbbox((0, 0), footer_text, font=caption_font)
    footer_width = footer_bbox[2] - footer_bbox[0]
    footer_x = (canvas_width - footer_width) // 2
    footer_y = canvas_height - 35  # 稍微调整位置
    draw.text((footer_x, footer_y), footer_text, fill=colors['text'], font=caption_font)
    
    return canvas

def save_high_quality_outputs(canvas, output_dir):
    """保存高质量的多格式输出"""
    print("保存高质量图表文件...")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    files_saved = []

    # 保存PNG格式（300 DPI）
    try:
        png_file = os.path.join(output_dir, "分子骨架真实结构图_RDKit_PIL.png")
        canvas.save(png_file, 'PNG', dpi=(300, 300))
        print(f"PNG图片保存完成: {png_file}")
        files_saved.append(png_file)
    except Exception as e:
        print(f"PNG保存失败: {str(e)}")

    # 保存PDF格式
    try:
        pdf_file = os.path.join(output_dir, "分子骨架真实结构图_RDKit_PIL.pdf")
        canvas.save(pdf_file, 'PDF', resolution=300)
        print(f"PDF矢量图保存完成: {pdf_file}")
        files_saved.append(pdf_file)
    except Exception as e:
        print(f"PDF保存失败: {str(e)}")

    # 保存JPEG格式（备选）
    try:
        jpeg_file = os.path.join(output_dir, "分子骨架真实结构图_RDKit_PIL.jpg")
        # 转换为RGB模式（JPEG不支持透明度）
        rgb_canvas = Image.new('RGB', canvas.size, (255, 255, 255))
        rgb_canvas.paste(canvas, mask=canvas.split()[-1] if canvas.mode == 'RGBA' else None)
        rgb_canvas.save(jpeg_file, 'JPEG', quality=95, dpi=(300, 300))
        print(f"JPEG图片保存完成: {jpeg_file}")
        files_saved.append(jpeg_file)
    except Exception as e:
        print(f"JPEG保存失败: {str(e)}")

    return files_saved

def main():
    """主函数"""
    # 设置样式
    colors, font_options = setup_chinese_academic_style()
    
    # 加载数据
    scaffolds = load_scaffold_data()
    if not scaffolds:
        print("⚠ 无法加载数据，程序退出")
        return
    
    # 验证SMILES并创建分子对象
    valid_data = validate_and_create_molecules(scaffolds)
    if len(valid_data) == 0:
        print("⚠ 没有有效的分子结构，程序退出")
        return
    
    # 创建网格布局
    canvas = create_grid_layout(valid_data, colors, font_options)
    
    # 保存图表
    output_dir = "03_图表输出/论文级图表"
    files_saved = save_high_quality_outputs(canvas, output_dir)
    
    print("\n=== 分子骨架真实结构图生成完成 ===")
    print("生成的文件:")
    for file_path in files_saved:
        print(f"- {file_path}")
    print("图表特点:")
    print("- 真实的2D分子结构图（RDKit生成）")
    print("- 4×5网格布局展示20个分子骨架")
    print("- Nature期刊风格设计")
    print("- 中国风学术配色方案")
    print("- 高分辨率输出(300 DPI)")
    print("- 稳定的最小依赖实现")

if __name__ == "__main__":
    main()
