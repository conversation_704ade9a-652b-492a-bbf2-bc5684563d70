---
description:
globs:
alwaysApply: false
---
name: Nature风格图表规范
description: 提供在R中创建符合Nature期刊发表标准的图表的详细指南，确保图表的专业性、清晰度和合规性。
---
# Nature 期刊风格图表规范

## 1. 核心原则
所有图表设计必须以**清晰传达科学信息**为唯一目标。图表应独立成章（self-contained），即读者仅通过图表及其标题图例就能理解其核心内容。**优先使用矢量格式（PDF, SVG, EPS）**进行保存，以确保无限缩放下的清晰度。

## 2. 字体 (Typography)
- **主字体**: 必须使用 **Arial** 或 **Helvetica**。这是Nature系列期刊的强制要求。
- **字号 (Font Size)**: 正文和标签的推荐字号范围为 **5-7 pt** (points)。确保所有文本在最终排版尺寸下（单栏或双栏）都清晰可读。在R中，`theme_bw(base_size = 7)` 是一个好的起点。
- **字重**: 避免使用粗体，除非有极强的理由需要强调。

## 3. 尺寸与分辨率 (Sizing & Resolution)
- **标准宽度**:
    - **单栏**: 89 mm
    - **双栏**: 183 mm
- **分辨率**:
    - **位图 (PNG, TIFF)**: 提交时的分辨率**不得低于 300 DPI**。
    - **矢量图**: 无分辨率限制，是首选格式。

## 4. 颜色 (Color)
- **配色方案**: **必须使用色盲友好 (color-blind friendly) 的配色方案**。
    - **首选方案**: **Wong配色方案**，由《Nature Methods》官方推荐，对最常见的红绿色盲读者友好。
    - **Wong Palette (8色)**:
        - `#000000` (Black)
        - `#E69F00` (Orange)
        - `#56B4E9` (Sky Blue)
        - `#009E73` (Bluish Green)
        - `#F0E442` (Yellow)
        - `#0072B2` (Blue)
        - `#D55E00` (Vermilion)
        - `#CC79A7` (Reddish Purple)
- **颜色禁忌**:
    - **严禁使用纯红色和纯绿色的直接对比**，这是对色盲读者最不友好的组合。
    - 避免使用过于饱和、鲜艳的颜色，选择柔和、专业的色调。
    - 背景色应为白色或透明。

## 5. 图表元素 (Chart Elements)
- **坐标轴 (Axes)**: 必须有清晰的标签，并明确注明**单位**（例如, "Concentration (μM)"）。坐标轴线应清晰可见。
- **图例 (Legend)**: 简洁明了，位置恰当（通常在右上角或图外），不与任何数据点重叠。
- **网格线 (Gridlines)**: **默认禁用所有网格线**。 `panel.grid.major = element_blank(), panel.grid.minor = element_blank()`。
- **边框 (Border)**: 图表必须有完整的黑色边框。`panel.border = element_rect(colour = "black", fill=NA, size=0.5)`。

## 6. R (ggplot2) 最佳实践
```R
# 强烈推荐的Nature主题函数
theme_nature <- function(base_size = 7, base_family = "Arial") {
  theme_bw(base_size = base_size, base_family = base_family) +
  theme(
    panel.grid.major = element_blank(),
    panel.grid.minor = element_blank(),
    panel.border = element_rect(colour = "black", fill = NA, size = 0.5),
    plot.title = element_text(hjust = 0.5, size = rel(1.1)),
    axis.text = element_text(color = "black"),
    axis.ticks = element_line(colour = "black", size = 0.5),
    legend.key = element_blank() # 移除图例项的背景
  )
}

# Wong配色方案向量
wong_colors <- c("#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7")

# 使用示例
# ggplot(data, aes(x = x_var, y = y_var, color = group)) +
#   geom_point() +
#   scale_color_manual(values = wong_colors) +
#   labs(title = "Figure Title", x = "X-axis Label (Unit)", y = "Y-axis Label (Unit)") +
#   theme_nature()

# 保存为PDF
# ggsave("figure.pdf", width = 89, height = 60, units = "mm", device = cairo_pdf)
```
**注意**: 在保存PDF时，若包含非英文字符，推荐使用 `cairo_pdf` 设备以正确嵌入字体。
