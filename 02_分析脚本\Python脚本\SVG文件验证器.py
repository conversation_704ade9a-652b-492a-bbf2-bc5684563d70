# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-14
# 描述: SVG文件验证器 - 检查SVG文件的有效性和属性

import os
import xml.etree.ElementTree as ET

def check_svg_file(svg_path):
    """检查SVG文件的有效性和属性"""
    print(f"=== SVG文件验证器 ===")
    print(f"检查文件: {svg_path}")
    
    # 检查文件是否存在
    if not os.path.exists(svg_path):
        print(f"✗ 文件不存在: {svg_path}")
        return False
    
    # 检查文件大小
    file_size = os.path.getsize(svg_path)
    print(f"✓ 文件大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
    
    try:
        # 解析SVG文件
        tree = ET.parse(svg_path)
        root = tree.getroot()
        
        # 检查根元素
        if root.tag.endswith('svg'):
            print("✓ SVG格式验证通过")
        else:
            print(f"✗ 不是有效的SVG文件，根元素: {root.tag}")
            return False
        
        # 获取SVG属性
        width = root.get('width', '未指定')
        height = root.get('height', '未指定')
        viewbox = root.get('viewBox', '未指定')
        
        print(f"✓ SVG尺寸: {width} × {height}")
        print(f"✓ ViewBox: {viewbox}")
        
        # 检查标题和描述
        title_elem = root.find('.//{http://www.w3.org/2000/svg}title')
        desc_elem = root.find('.//{http://www.w3.org/2000/svg}desc')
        
        if title_elem is not None:
            print(f"✓ 标题: {title_elem.text}")
        else:
            print("⚠ 未找到标题元素")
        
        if desc_elem is not None:
            print(f"✓ 描述: {desc_elem.text}")
        else:
            print("⚠ 未找到描述元素")
        
        # 检查图像元素
        image_elems = root.findall('.//{http://www.w3.org/2000/svg}image')
        print(f"✓ 包含图像元素数量: {len(image_elems)}")
        
        if image_elems:
            for i, img in enumerate(image_elems, 1):
                img_width = img.get('width', '未指定')
                img_height = img.get('height', '未指定')
                href = img.get('{http://www.w3.org/1999/xlink}href', '未指定')
                
                print(f"  图像 {i}: {img_width} × {img_height}")
                if href.startswith('data:image'):
                    data_type = href.split(';')[0].split(':')[1]
                    print(f"  数据类型: {data_type}")
                    print(f"  数据大小: {len(href)} 字符")
        
        print("✓ SVG文件验证完成")
        return True
        
    except ET.ParseError as e:
        print(f"✗ XML解析错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 验证失败: {e}")
        return False

def create_svg_info_report(svg_path):
    """创建SVG文件信息报告"""
    report_path = svg_path.replace('.svg', '_info.txt')
    
    print(f"\n生成信息报告: {report_path}")
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("SVG文件信息报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"文件路径: {svg_path}\n")
        f.write(f"生成时间: 2025-07-14\n")
        f.write(f"文件大小: {os.path.getsize(svg_path):,} 字节\n\n")
        
        try:
            tree = ET.parse(svg_path)
            root = tree.getroot()
            
            f.write("SVG属性:\n")
            f.write(f"- 宽度: {root.get('width', '未指定')}\n")
            f.write(f"- 高度: {root.get('height', '未指定')}\n")
            f.write(f"- ViewBox: {root.get('viewBox', '未指定')}\n\n")
            
            title_elem = root.find('.//{http://www.w3.org/2000/svg}title')
            desc_elem = root.find('.//{http://www.w3.org/2000/svg}desc')
            
            if title_elem is not None:
                f.write(f"标题: {title_elem.text}\n")
            if desc_elem is not None:
                f.write(f"描述: {desc_elem.text}\n")
            
            f.write("\n使用建议:\n")
            f.write("- 适合网页展示和在线文档\n")
            f.write("- 可在浏览器中直接打开查看\n")
            f.write("- 支持无损缩放\n")
            f.write("- 可用于专利申请的电子版文档\n")
            
        except Exception as e:
            f.write(f"解析错误: {e}\n")
    
    print(f"✓ 信息报告已保存")

def main():
    """主函数"""
    # 检查专利版SVG文件
    svg_file = "03_图表输出/论文级图表/分子骨架前10个_专利版.svg"
    
    print("分子骨架SVG文件验证器")
    print("=" * 40)
    
    if check_svg_file(svg_file):
        create_svg_info_report(svg_file)
        
        print("\n=== 使用建议 ===")
        print("1. 浏览器查看: 直接拖拽SVG文件到浏览器中打开")
        print("2. 专业软件: 可用Adobe Illustrator、Inkscape等编辑")
        print("3. 网页嵌入: 可直接嵌入HTML页面")
        print("4. 专利申请: 适合电子版专利文档")
        print("5. 文档插入: 可插入Word、PowerPoint等")
        
        print("\n=== SVG优势 ===")
        print("✓ 矢量格式，无损缩放")
        print("✓ 文件体积相对较小")
        print("✓ 网页友好，加载快速")
        print("✓ 可编辑，支持文本搜索")
        print("✓ 兼容性好，广泛支持")
    else:
        print("⚠ SVG文件验证失败，请检查文件")

if __name__ == "__main__":
    main()
