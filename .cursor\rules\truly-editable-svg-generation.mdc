---
description: 
globs: 
alwaysApply: false
---
name: 真正可编辑SVG图像生成规则
description: 确保生成的SVG文件是纯矢量格式，可在专业矢量软件中完全编辑，并符合学术发表和专利申请的高标准。
---
# 规则：生成真正可编辑的SVG分子结构图

**目标**: 确保所有生成的SVG文件都是纯矢量格式，可在Adobe Illustrator, Inkscape等专业软件中进行无限制的后期编辑。杜绝任何形式的位图嵌入。

---

### 1. 技术要求

- **[强制] 纯矢量输出**: 生成的SVG文件中**不得**包含`<image>`标签或Base64编码的位图数据。所有图形元素必须是`<path>`, `<circle>`, `<line>`, `<text>`等SVG原生矢量元素。
- **[强制] 独立元素**: 每个原子、化学键、标签都应是独立的SVG元素，或包含在逻辑分组`<g>`中，而不是合并成单一的、不可编辑的路径。
- **[推荐] 软件兼容性**: 优先生成符合SVG 1.1标准的文档，以确保在主流矢量编辑软件（Adobe Illustrator, Inkscape, CorelDRAW, Affinity Designer）中的最大兼容性。

---

### 2. 结构规范

- **[强制] 语义化ID**: 为关键元素和分组提供有意义的`id`。
  - **分组**: `<g id="molecule-Scaffold_1">`, `<g id="structure-Scaffold_1">`
  - **标签**: `<text id="label-Scaffold_1">`
- **[推荐] 逻辑分组 (`<g>`)**: 将属于同一分子结构的所有元素（键、原子、标签）放入一个`<g>`标签内。这使得在编辑器中可以轻松地将整个分子作为一个对象进行移动、缩放或隐藏。
- **[推荐] CSS样式**: 使用`<style>`标签在SVG头部定义CSS类，而不是将样式硬编码到每个元素的`style`属性中。
  ```xml
  <defs>
    <style type="text/css"><![CDATA[
      .bond { stroke: #000000; stroke-width: 2px; }
      .atom-label { font-family: Arial; font-size: 16px; }
    ]]></style>
  </defs>
  ```
- **[强制] 元数据**: 必须包含`<title>`和`<desc>`标签，简要说明图像内容。
  ```xml
  <svg ...>
    <title>分子骨架结构图 (Top 10 Scaffolds)</title>
    <desc>包含10个最常见分子骨架的化学结构，矢量格式，专为学术发表和专利申请设计。</desc>
    ...
  </svg>
  ```

---

### 3. 代码实现指导 (Python - RDKit)

- **[强制] 使用 `rdMolDraw2D.MolDraw2DSVG`**: 这是生成高质量、纯矢量SVG的**唯一官方推荐方法**。
  ```python
  from rdkit import Chem
  from rdkit.Chem.Draw import rdMolDraw2D

  mol = Chem.MolFromSmiles('c1ccccc1')
  drawer = rdMolDraw2D.MolDraw2DSVG(300, 300) # 宽, 高
  drawer.DrawMolecule(mol)
  drawer.FinishDrawing()
  svg_content = drawer.GetDrawingText()
  # svg_content 现在是纯矢量的SVG字符串
  ```
- **[强制] 严禁使用位图转换流程**: 绝对禁止使用`rdkit.Chem.Draw.MolToImage()` (生成PIL Image) 之后再将其保存或转换为SVG的流程。这种方法只会产生嵌入位图的SVG容器。
- **[推荐] 错误处理**: 对于无法处理的SMILES或分子结构，应捕获异常并提供明确的错误信息或生成一个占位符SVG，而不是让程序崩溃。

---

### 4. 质量验证标准

- **[自动] 代码审查**: 检查生成SVG的Python脚本，确认其是否使用了`rdMolDraw2D.MolDraw2DSVG`。
- **[自动] 内容验证**:
  - 使用文本搜索确认SVG文件中**不含** `<image` 或 `base64` 字符串。
  - 确认文件中包含大量的`<path>`、`<g>`和`<text>`标签。
- **[手动] 软件测试**: **必须**在至少一个专业矢量编辑软件（推荐Inkscape，免费开源）中打开生成的SVG文件。
  - **测试项1**: 尝试取消组合（Ungroup）整个图像，看是否能分离出独立的键和原子。
  - **测试项2**: 尝试单独选中并修改一个化学键的颜色或粗细。
  - **测试项3**: 尝试编辑文本标签的字体或内容。
- **[自动/手动] 文件大小**: 检查文件大小。纯矢量SVG（特别是简单的分子）通常很小（几KB到几十KB）。如果文件大小达到几百KB或MB，很可能嵌入了位图。

---

### 5. 应用场景适配

- **学术论文/专利**: 必须使用此规则，确保图表符合出版和审查的最高标准。
- **网页/演示**: 强烈推荐使用此规则，以获得清晰、可缩放且加载快速的视觉效果。

---

### 6. 项目参考 (Project References)

- **最佳实践脚本**: [分子骨架可编辑SVG生成器.py](mdc:02_分析脚本/Python脚本/分子骨架可编辑SVG生成器.py) 完整展示了如何遵循此规则生成高质量可编辑SVG。
- **质量验证工具**: [SVG质量验证工具包.py](mdc:02_分析脚本/Python脚本/SVG质量验证工具包.py) 可用于自动化检查生成的SVG文件是否符合矢量标准。
- **合规输出示例**: [分子骨架前10个_可编辑版.svg](mdc:03_图表输出/论文级图表/分子骨架前10个_可编辑版.svg) 是一个遵循此规则生成的、可在Illustrator中完全编辑的SVG文件。

