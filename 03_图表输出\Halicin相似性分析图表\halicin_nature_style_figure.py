# Author: ZK
# Email: <EMAIL>
# Date: 2025-08-03
# Description: Generate Nature-style figure with molecular structures for Halicin similarity analysis

import pandas as pd
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np
from rdkit import Chem
from rdkit.Chem import Draw, rdMolDescriptors
from rdkit.Chem.Draw import rdMolDraw2D
from PIL import Image, ImageDraw, ImageFont
import io
import os

# Set font for Nature style (Arial/Helvetica)
plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['font.size'] = 7  # Nature standard: 5-7pt
plt.rcParams['axes.unicode_minus'] = False

# Read data
data = pd.read_csv('halicin_similarity_data.csv')

# Halicin reference structure
halicin_smiles = "C1=CC=C(C(=C1)C(=O)NC2=NC=CS2)[N+](=O)[O-]"

def draw_molecule_structure(smiles, size=(200, 150)):
    """Draw molecular structure using RDKit"""
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return None
            
        # Use RDKit to draw molecule
        drawer = rdMolDraw2D.MolDraw2DCairo(size[0], size[1])
        drawer.SetFontSize(0.8)  # Smaller font for cleaner look
        drawer.DrawMolecule(mol)
        drawer.FinishDrawing()
        
        # Get image data
        img_data = drawer.GetDrawingText()
        img = Image.open(io.BytesIO(img_data))
        
        return img
        
    except Exception as e:
        print(f"Error drawing molecule: {e}")
        return None

def create_nature_style_figure():
    """Create Nature-style figure with molecular structures and similarity data"""
    
    # Sort data by similarity (descending)
    data_sorted = data.sort_values('Tanimoto_Sim_To_Halicin', ascending=False)
    
    # Create figure with specific Nature dimensions
    fig_width = 183/25.4  # 183mm converted to inches (double column width)
    fig_height = 120/25.4  # 120mm converted to inches
    
    fig, ax = plt.subplots(1, 1, figsize=(fig_width, fig_height))
    
    # Define colors (Wong palette - Nature recommended)
    wong_colors = {
        'blue': '#0072B2',
        'orange': '#E69F00', 
        'sky_blue': '#56B4E9',
        'green': '#009E73',
        'yellow': '#F0E442',
        'vermillion': '#D55E00',
        'purple': '#CC79A7',
        'black': '#000000'
    }
    
    # Create layout: molecular structures on left, similarity bars on right
    n_compounds = len(data_sorted)
    y_positions = np.arange(n_compounds)
    
    # Draw similarity bars
    bars = ax.barh(y_positions, data_sorted['Tanimoto_Sim_To_Halicin'], 
                   height=0.6, color=wong_colors['blue'], alpha=0.8,
                   edgecolor='black', linewidth=0.5)
    
    # Add similarity values as text
    for i, (idx, row) in enumerate(data_sorted.iterrows()):
        similarity = row['Tanimoto_Sim_To_Halicin']
        ax.text(similarity + 0.01, i, f'{similarity:.3f}', 
                va='center', ha='left', fontsize=6, fontweight='bold')
    
    # Set compound names on y-axis
    compound_names = [f"{row['Name']}" for _, row in data_sorted.iterrows()]
    ax.set_yticks(y_positions)
    ax.set_yticklabels(compound_names, fontsize=6)
    
    # Set x-axis
    ax.set_xlabel('Tanimoto Similarity Index', fontsize=7, fontweight='bold')
    ax.set_xlim(0, 0.45)
    ax.set_xticks(np.arange(0, 0.5, 0.1))
    
    # Add title
    ax.set_title('Structural Similarity to Halicin', fontsize=8, fontweight='bold', pad=10)
    
    # Nature style formatting
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_linewidth(0.5)
    ax.spines['bottom'].set_linewidth(0.5)
    ax.tick_params(width=0.5, length=3)
    ax.grid(False)
    
    # Add reference line for Halicin (similarity = 1.0, but not shown)
    ax.axvline(x=0.3, color='red', linestyle='--', alpha=0.5, linewidth=1)
    ax.text(0.31, n_compounds-0.5, 'High similarity\nthreshold', 
            fontsize=5, color='red', va='top')
    
    plt.tight_layout()
    
    # Save figure
    plt.savefig('Halicin_Similarity_Nature_Style.pdf', dpi=300, bbox_inches='tight')
    plt.savefig('Halicin_Similarity_Nature_Style.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Nature-style similarity figure saved as:")
    print("- Halicin_Similarity_Nature_Style.pdf")
    print("- Halicin_Similarity_Nature_Style.png")

def create_molecular_structure_panel():
    """Create a separate panel showing molecular structures"""
    
    # Sort data by similarity
    data_sorted = data.sort_values('Tanimoto_Sim_To_Halicin', ascending=False)
    
    # Create figure for molecular structures
    n_compounds = len(data_sorted) + 1  # +1 for Halicin reference
    n_cols = 4  # 4 columns for better layout
    n_rows = (n_compounds + n_cols - 1) // n_cols
    
    fig_width = 183/25.4  # Nature double column width
    fig_height = n_rows * 40/25.4  # Adjust height based on rows
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(fig_width, fig_height))
    
    # Ensure axes is 2D array
    if n_rows == 1:
        axes = axes.reshape(1, -1)
    if n_cols == 1:
        axes = axes.reshape(-1, 1)
    
    # First, draw Halicin reference
    halicin_img = draw_molecule_structure(halicin_smiles)
    if halicin_img:
        axes[0, 0].imshow(halicin_img)
        axes[0, 0].set_title('Halicin\n(Reference)', fontsize=7, fontweight='bold', 
                            color='red', pad=5)
    axes[0, 0].axis('off')
    
    # Draw other compounds
    for idx, (_, row) in enumerate(data_sorted.iterrows()):
        plot_idx = idx + 1  # +1 because Halicin is at index 0
        row_idx = plot_idx // n_cols
        col_idx = plot_idx % n_cols
        
        if row_idx < n_rows and col_idx < n_cols:
            mol_img = draw_molecule_structure(row['SMILES'])
            
            if mol_img:
                axes[row_idx, col_idx].imshow(mol_img)
                
                # Color code by similarity
                similarity = row['Tanimoto_Sim_To_Halicin']
                if similarity > 0.3:
                    color = '#D55E00'  # Vermillion for high similarity
                elif similarity > 0.25:
                    color = '#E69F00'  # Orange for medium similarity
                else:
                    color = '#0072B2'  # Blue for lower similarity
                
                title = f"{row['Name']}\nSim: {similarity:.3f}"
                axes[row_idx, col_idx].set_title(title, fontsize=6, color=color, pad=3)
            
            axes[row_idx, col_idx].axis('off')
    
    # Hide empty subplots
    for idx in range(n_compounds, n_rows * n_cols):
        row_idx = idx // n_cols
        col_idx = idx % n_cols
        if row_idx < n_rows and col_idx < n_cols:
            axes[row_idx, col_idx].axis('off')
    
    # Set main title
    fig.suptitle('Molecular Structures of Halicin-Similar Compounds', 
                 fontsize=9, fontweight='bold', y=0.98)
    
    # Add similarity legend
    legend_elements = [
        plt.Rectangle((0,0),1,1, facecolor='#D55E00', label='High similarity (>0.30)'),
        plt.Rectangle((0,0),1,1, facecolor='#E69F00', label='Medium similarity (0.25-0.30)'),
        plt.Rectangle((0,0),1,1, facecolor='#0072B2', label='Lower similarity (<0.25)')
    ]
    fig.legend(handles=legend_elements, loc='lower center', ncol=3, 
               fontsize=6, bbox_to_anchor=(0.5, 0.02))
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.93, bottom=0.1)
    
    # Save figure
    plt.savefig('Halicin_Molecular_Structures_Panel.pdf', dpi=300, bbox_inches='tight')
    plt.savefig('Halicin_Molecular_Structures_Panel.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Molecular structures panel saved as:")
    print("- Halicin_Molecular_Structures_Panel.pdf")
    print("- Halicin_Molecular_Structures_Panel.png")

def create_combined_nature_figure():
    """Create a combined Nature-style figure with both structures and similarity data"""
    
    data_sorted = data.sort_values('Tanimoto_Sim_To_Halicin', ascending=False)
    
    # Create figure with Nature double-column dimensions
    fig_width = 183/25.4  # 183mm in inches
    fig_height = 120/25.4  # 120mm in inches
    
    fig = plt.figure(figsize=(fig_width, fig_height))
    
    # Create grid layout: structures on left, similarity plot on right
    gs = fig.add_gridspec(2, 8, hspace=0.3, wspace=0.3)
    
    # Molecular structures (left side, 2x4 grid)
    struct_axes = []
    for i in range(2):
        for j in range(4):
            ax = fig.add_subplot(gs[i, j])
            struct_axes.append(ax)
    
    # Similarity plot (right side)
    sim_ax = fig.add_subplot(gs[:, 4:])
    
    # Draw molecular structures
    # First Halicin
    halicin_img = draw_molecule_structure(halicin_smiles, size=(150, 100))
    if halicin_img:
        struct_axes[0].imshow(halicin_img)
        struct_axes[0].set_title('Halicin\n(Ref.)', fontsize=6, fontweight='bold', color='red')
    struct_axes[0].axis('off')
    
    # Then other compounds
    for idx, (_, row) in enumerate(data_sorted.iterrows()):
        if idx + 1 < len(struct_axes):
            mol_img = draw_molecule_structure(row['SMILES'], size=(150, 100))
            if mol_img:
                struct_axes[idx + 1].imshow(mol_img)
                similarity = row['Tanimoto_Sim_To_Halicin']
                color = '#D55E00' if similarity > 0.3 else '#E69F00' if similarity > 0.25 else '#0072B2'
                struct_axes[idx + 1].set_title(f"{row['Name'][:12]}\n{similarity:.3f}", 
                                             fontsize=5, color=color)
            struct_axes[idx + 1].axis('off')
    
    # Hide unused structure axes
    for i in range(len(data_sorted) + 1, len(struct_axes)):
        struct_axes[i].axis('off')
    
    # Draw similarity plot
    y_pos = np.arange(len(data_sorted))
    bars = sim_ax.barh(y_pos, data_sorted['Tanimoto_Sim_To_Halicin'], 
                       height=0.6, color='#0072B2', alpha=0.8,
                       edgecolor='black', linewidth=0.5)
    
    # Add similarity values
    for i, (_, row) in enumerate(data_sorted.iterrows()):
        sim_ax.text(row['Tanimoto_Sim_To_Halicin'] + 0.01, i, 
                   f"{row['Tanimoto_Sim_To_Halicin']:.3f}", 
                   va='center', fontsize=5, fontweight='bold')
    
    # Format similarity plot
    sim_ax.set_yticks(y_pos)
    sim_ax.set_yticklabels([row['Name'] for _, row in data_sorted.iterrows()], fontsize=6)
    sim_ax.set_xlabel('Tanimoto Similarity', fontsize=7, fontweight='bold')
    sim_ax.set_title('Similarity to Halicin', fontsize=7, fontweight='bold')
    sim_ax.set_xlim(0, 0.45)
    
    # Nature style formatting
    sim_ax.spines['top'].set_visible(False)
    sim_ax.spines['right'].set_visible(False)
    sim_ax.spines['left'].set_linewidth(0.5)
    sim_ax.spines['bottom'].set_linewidth(0.5)
    sim_ax.tick_params(width=0.5, length=3, labelsize=6)
    sim_ax.grid(False)
    
    # Add main title
    fig.suptitle('Structural Similarity Analysis of Halicin-Related Compounds', 
                 fontsize=8, fontweight='bold', y=0.95)
    
    # Save combined figure
    plt.savefig('Halicin_Combined_Nature_Figure.pdf', dpi=300, bbox_inches='tight')
    plt.savefig('Halicin_Combined_Nature_Figure.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Combined Nature-style figure saved as:")
    print("- Halicin_Combined_Nature_Figure.pdf")
    print("- Halicin_Combined_Nature_Figure.png")

if __name__ == "__main__":
    print("=== Generating Nature-style Halicin similarity figures ===")
    
    try:
        # Generate all three types of figures
        create_nature_style_figure()
        create_molecular_structure_panel()
        create_combined_nature_figure()
        
        print("\n=== All Nature-style figures generated successfully ===")
        print("\nGenerated files:")
        print("1. Halicin_Similarity_Nature_Style.pdf/png - Clean similarity plot")
        print("2. Halicin_Molecular_Structures_Panel.pdf/png - Molecular structures panel")
        print("3. Halicin_Combined_Nature_Figure.pdf/png - Combined figure (RECOMMENDED)")
        
    except Exception as e:
        print(f"Error generating figures: {e}")
        import traceback
        traceback.print_exc()
