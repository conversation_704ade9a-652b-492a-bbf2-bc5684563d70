# 分子骨架前10个专利版图表说明

**作者**: ZK  
**邮箱**: <EMAIL>  
**日期**: 2025-07-14  
**版本**: 专利版 v1.0

## 📋 专利版特点

### 🎯 **专为专利申请优化**
- ✅ **精选10个骨架**：从20个中筛选出最具代表性的前10个
- ✅ **排除Scaffold_22**：按您要求移除第22号骨架
- ✅ **2×5布局**：更适合专利文档的横向布局
- ✅ **清晰标识**：每个分子骨架都有明确的ID标识
- ✅ **专利友好格式**：提供PNG、PDF、JPEG三种格式

### 📊 **布局优化**
- **网格布局**：2行×5列，紧凑而清晰
- **分子尺寸**：350×350像素，比20个版本稍大
- **字体增大**：标题32pt，ID标识24pt，更易阅读
- **边框加粗**：3像素边框，突出每个分子结构
- **专利标题**：明确标注"Representative Molecular Scaffolds for Patent Application"

## 📁 生成的文件

### 🎨 **输出文件**
1. **分子骨架前10个_专利版.png** - 高分辨率PNG (300 DPI)
2. **分子骨架前10个_专利版.pdf** - 矢量PDF格式
3. **分子骨架前10个_专利版.svg** - SVG矢量格式（新增）
4. **分子骨架前10个_专利版.jpg** - JPEG格式（文件较小）

### 📍 **文件位置**
```
03_图表输出/论文级图表/
├── 分子骨架前10个_专利版.png
├── 分子骨架前10个_专利版.pdf
├── 分子骨架前10个_专利版.svg  # 新增SVG格式
└── 分子骨架前10个_专利版.jpg
```

## 🧬 选中的分子骨架列表

| 序号 | Scaffold ID | 化学结构描述 | SMILES |
|------|-------------|--------------|---------|
| 1 | Scaffold_1 | Xanthone core structure | O=c1c2ccccc2oc2ccccc12 |
| 2 | Scaffold_2 | Benzofuran-coumarin hybrid | O=c1ccc2cc3ccoc3cc2o1 |
| 3 | Scaffold_3 | Benzene ring | c1ccccc1 |
| 4 | Scaffold_4 | Phenyl-substituted coumarin | O=c1cc(-c2ccccc2)oc2ccccc12 |
| 5 | Scaffold_5 | Thiophene-coumarin with piperidine | O=C1/C(=C/c2cccs2)Oc2c(CN3CCCCC3)cccc21 |
| 6 | Scaffold_12 | Coumarin core | O=c1ccc2ccccc2o1 |
| 7 | Scaffold_13 | Fused pyrimidine-benzofuran | O=c1[nH]cnc2c1oc1ncccc12 |
| 8 | Scaffold_14 | Phenoxy-coumarin with piperidine | O=c1c(Oc2ccccc2)coc2c(CN3CCCCC3)cccc12 |
| 9 | Scaffold_15 | Phenyl-coumarin | O=c1c(-c2ccccc2)coc2ccccc12 |
| 10 | Scaffold_27 | Extended phenyl-coumarin system | O=c1ccc2cc3c(-c4ccccc4)coc3cc2o1 |

**注意**：已按要求排除Scaffold_22 (Carbazole-quinone)

## 🎨 设计规范

### 🌈 **配色方案**
- **朱红色** (#C0392B)：Scaffold ID标识
- **靛蓝色** (#2980B9)：分子边框
- **青金色** (#34495E)：标题和说明文字
- **定窑白** (#FDFEFE)：背景色
- **标准黑** (#000000)：分子结构线条

### 📐 **尺寸规格**
- **总画布**：约2030×1200像素
- **单个分子**：350×350像素
- **分辨率**：300 DPI（适合打印）
- **边距**：80像素
- **间距**：50像素

### 🔤 **字体设置**
- **标题字体**：32pt，加粗
- **ID字体**：24pt，朱红色
- **说明字体**：16pt，标准色

## 🚀 使用方法

### 📝 **快速生成**
```bash
python "02_分析脚本/Python脚本/分子骨架前10个_专利版.py"
```

### 🔧 **环境要求**
- Python 3.8+
- RDKit (>=2023.3.1)
- Pillow (>=9.0.0)

### 📋 **专利申请建议**

#### 🎯 **推荐用途**
- **专利说明书**：使用PNG格式插入文档
- **专利附图**：PDF格式便于缩放和编辑
- **在线提交**：JPEG格式文件较小，上传快速

#### 📏 **格式选择**
- **打印版专利**：推荐PNG (300 DPI)
- **电子版专利**：推荐PDF或SVG (矢量格式)
- **网络提交**：推荐JPEG (文件较小)
- **网页展示**：推荐SVG (可缩放，体积小)

#### 🔍 **质量保证**
- 所有分子结构均经过RDKit验证
- 300 DPI分辨率确保打印清晰
- 标准化学结构表示，符合专利要求

## 📊 与20个版本对比

| 特性 | 20个版本 | 10个专利版 |
|------|----------|------------|
| **分子数量** | 20个 | 10个 |
| **布局** | 4×5 | 2×5 |
| **分子尺寸** | 320×320px | 350×350px |
| **总尺寸** | 约1800×2200px | 约2030×1200px |
| **适用场景** | 学术论文 | 专利申请 |
| **标题** | 学术标题 | 专利标题 |
| **Scaffold_22** | 包含 | 已排除 |

## ⚡ 技术特点

### 🔬 **分子结构质量**
- **RDKit生成**：业界标准的化学结构绘制
- **标准化表示**：符合IUPAC化学结构规范
- **高清渲染**：350×350像素确保细节清晰
- **专业外观**：黑色线条，白色背景，经典配色

### 🎨 **视觉设计**
- **中国风配色**：传统色彩与现代设计结合
- **专利友好**：清晰的标识和布局
- **可读性强**：大字体，高对比度
- **专业规范**：符合专利文档标准

### 💾 **文件格式**
- **PNG**：无损压缩，适合打印，300 DPI高分辨率
- **PDF**：矢量格式，可无损缩放，适合专业编辑
- **SVG**：矢量格式，网页友好，文件小，可编辑
- **JPEG**：有损压缩，文件较小，适合网络传输

## 📞 使用支持

### 🔧 **技术支持**
如需修改或定制：
1. 调整分子数量：修改筛选逻辑
2. 更改布局：调整 `n_cols` 和 `n_rows` 参数
3. 修改尺寸：调整 `mol_size` 参数
4. 更换配色：修改 `colors` 字典

### 📧 **联系方式**
- **邮箱**：<EMAIL>
- **项目**：毕业课题研究 - 分子骨架可视化

---

**专利申请提示**：本图表展示了10个具有代表性的分子骨架结构，已排除Scaffold_22，适合作为专利申请中的技术附图使用。所有分子结构均为真实的2D化学结构图，符合专利文档的技术要求。
