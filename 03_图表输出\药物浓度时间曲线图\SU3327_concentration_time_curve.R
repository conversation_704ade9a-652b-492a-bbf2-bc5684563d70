# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-08-03
# 描述: 绘制SU3327药物浓度随时间变化的Nature风格折线图

# 加载必要的包
library(ggplot2)
library(dplyr)
library(scales)

# 设置工作目录
setwd("E:/毕业课题/毕业课题/模式数据/03_图表输出/药物浓度时间曲线图")

# 创建数据
time_points <- c(0, 15, 30, 60)  # 时间点 (分钟)
concentration <- c(728, 393, 118, 0)  # SU3327浓度 (ppb)

# 创建数据框
drug_data <- data.frame(
  Time = time_points,
  Concentration = concentration
)

# Nature风格主题函数 (使用系统默认字体)
theme_nature <- function(base_size = 10) {
  theme_bw(base_size = base_size) +
  theme(
    panel.grid.major = element_blank(),
    panel.grid.minor = element_blank(),
    panel.border = element_rect(colour = "black", fill = NA, linewidth = 0.5),
    plot.title = element_text(hjust = 0.5, size = rel(1.1)),
    axis.text = element_text(color = "black", size = rel(1.0)),
    axis.title = element_text(color = "black", size = rel(1.1)),
    axis.ticks = element_line(colour = "black", linewidth = 0.5),
    legend.key = element_blank(),
    plot.margin = margin(10, 10, 10, 10)
  )
}

# Wong配色方案 (Nature推荐的色盲友好配色)
wong_colors <- c("#000000", "#E69F00", "#56B4E9", "#009E73", 
                 "#F0E442", "#0072B2", "#D55E00", "#CC79A7")

# 创建折线图
p <- ggplot(drug_data, aes(x = Time, y = Concentration)) +
  # 添加折线
  geom_line(color = wong_colors[6], linewidth = 1.2, linetype = "solid") +  # 使用蓝色
  # 添加数据点
  geom_point(color = wong_colors[6], size = 2.5, shape = 16) +
  # 添加数据标签
  geom_text(aes(label = paste0(Concentration, " ppb")), 
            vjust = -0.8, hjust = 0.5, size = 2.5, color = "black") +
  # 设置坐标轴标签
  labs(
    title = "SU3327药物浓度随时间变化曲线",
    x = "时间 (分钟)",
    y = "SU3327浓度 (ppb)"
  ) +
  # 设置坐标轴范围和刻度
  scale_x_continuous(
    breaks = c(0, 15, 30, 45, 60),
    limits = c(-2, 65),
    expand = c(0, 0)
  ) +
  scale_y_continuous(
    breaks = seq(0, 800, 100),
    limits = c(0, 800),
    expand = c(0, 0),
    labels = comma_format()
  ) +
  # 应用Nature主题
  theme_nature()

# 显示图表
print(p)

# 保存为PDF格式 (Nature期刊首选的矢量格式)
ggsave("SU3327_concentration_time_curve.pdf", 
       plot = p,
       width = 89, 
       height = 60, 
       units = "mm", 
       device = cairo_pdf,
       dpi = 300)

# 保存为PNG格式 (备用格式)
ggsave("SU3327_concentration_time_curve.png", 
       plot = p,
       width = 89, 
       height = 60, 
       units = "mm", 
       dpi = 300)

# 保存为SVG格式 (可编辑矢量格式)
ggsave("SU3327_concentration_time_curve.svg", 
       plot = p,
       width = 89, 
       height = 60, 
       units = "mm")

# 输出数据摘要
cat("=== SU3327药物浓度数据摘要 ===\n")
print(drug_data)
cat("\n浓度下降趋势分析:\n")
cat("- 初始浓度 (0分钟):", drug_data$Concentration[1], "ppb\n")
cat("- 15分钟后浓度:", drug_data$Concentration[2], "ppb\n")
cat("- 30分钟后浓度:", drug_data$Concentration[3], "ppb\n")
cat("- 60分钟后浓度:", drug_data$Concentration[4], "ppb\n")

# 计算浓度下降百分比
initial_conc <- drug_data$Concentration[1]
for(i in 2:nrow(drug_data)) {
  reduction_percent <- round((initial_conc - drug_data$Concentration[i]) / initial_conc * 100, 1)
  cat("- ", drug_data$Time[i], "分钟时浓度下降了", reduction_percent, "%\n")
}

cat("\n图表已保存为以下格式:\n")
cat("- PDF: SU3327_concentration_time_curve.pdf (Nature期刊首选)\n")
cat("- PNG: SU3327_concentration_time_curve.png (高分辨率位图)\n")
cat("- SVG: SU3327_concentration_time_curve.svg (可编辑矢量图)\n")
