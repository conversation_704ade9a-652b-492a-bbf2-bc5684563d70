# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-08-03
# 描述: 绘制SU3327药物浓度随时间变化的Nature风格折线图

# 加载必要的包
library(ggplot2)
library(dplyr)
library(scales)

# 设置工作目录
setwd("E:/毕业课题/毕业课题/模式数据/03_图表输出/药物浓度时间曲线图")

# 创建数据 (根据实际色谱图数据)
time_points <- c(0, 15, 30, 60)  # 时间点 (分钟)
concentration <- c(0, 728, 393, 118)  # SU3327浓度 (ppb) - 根据实际检测数据

# 创建数据框
drug_data <- data.frame(
  Time = time_points,
  Concentration = concentration
)

# Nature风格主题函数 (严格遵循Nature期刊标准，使用系统默认字体)
theme_nature <- function(base_size = 7) {
  theme_bw(base_size = base_size) +
  theme(
    panel.grid.major = element_blank(),
    panel.grid.minor = element_blank(),
    panel.border = element_rect(colour = "black", fill = NA, linewidth = 0.5),
    plot.title = element_text(hjust = 0.5, size = rel(1.1), color = "black"),
    axis.text = element_text(color = "black", size = rel(1.0)),
    axis.title = element_text(color = "black", size = rel(1.0)),
    axis.ticks = element_line(colour = "black", linewidth = 0.5),
    legend.key = element_blank(),
    plot.margin = margin(5, 5, 5, 5, "mm")  # 适当的边距
  )
}

# Wong配色方案 (Nature期刊官方推荐的色盲友好配色)
wong_colors <- c("#000000", "#E69F00", "#56B4E9", "#009E73",
                 "#F0E442", "#0072B2", "#D55E00", "#CC79A7")

# 创建折线图 (严格遵循Nature期刊标准)
p <- ggplot(drug_data, aes(x = Time, y = Concentration)) +
  # 添加折线 (使用Wong配色方案的蓝色)
  geom_line(color = wong_colors[6], linewidth = 1.0,
            linetype = "solid") +  # #0072B2 蓝色
  # 添加数据点
  geom_point(color = wong_colors[6], size = 2.0, shape = 16) +
  # 添加数据标签 (手动优化每个标签位置避免重叠)
  annotate("text", x = 0, y = 50, label = "0 ppb",
           size = 2.0, color = "black", hjust = 0.5) +
  annotate("text", x = 15, y = 780, label = "728 ppb",
           size = 2.0, color = "black", hjust = 0.5) +
  annotate("text", x = 30, y = 440, label = "393 ppb",
           size = 2.0, color = "black", hjust = 0.5) +
  annotate("text", x = 60, y = 170, label = "118 ppb",
           size = 2.0, color = "black", hjust = 0.5) +
  # 设置坐标轴标签 (英文)
  labs(
    title = "SU3327 Drug Concentration vs Time",
    x = "Time (min)",
    y = "SU3327 Concentration (ppb)"
  ) +
  # 设置坐标轴范围和刻度 (为标签留出足够空间)
  scale_x_continuous(
    breaks = c(0, 15, 30, 45, 60),
    limits = c(-5, 65),
    expand = c(0, 0)
  ) +
  scale_y_continuous(
    breaks = seq(0, 800, 100),
    limits = c(-50, 850),  # 为顶部标签留出更多空间
    expand = c(0, 0),
    labels = comma_format()
  ) +
  # 应用Nature主题 (使用系统默认字体和7pt基础大小)
  theme_nature(base_size = 7)

# 显示图表
print(p)

# 保存为PDF格式 (Nature期刊首选的矢量格式)
ggsave("SU3327_concentration_time_curve.pdf", 
       plot = p,
       width = 89, 
       height = 60, 
       units = "mm", 
       device = cairo_pdf,
       dpi = 300)

# 保存为PNG格式 (备用格式)
ggsave("SU3327_concentration_time_curve.png", 
       plot = p,
       width = 89, 
       height = 60, 
       units = "mm", 
       dpi = 300)

# 保存为SVG格式 (可编辑矢量格式)
ggsave("SU3327_concentration_time_curve.svg", 
       plot = p,
       width = 89, 
       height = 60, 
       units = "mm")

# 输出数据摘要 (英文版本)
cat("=== SU3327 Drug Concentration Data Summary ===\n")
print(drug_data)
cat("\nConcentration Trend Analysis:\n")
cat("- Initial concentration (0 min):", drug_data$Concentration[1], "ppb\n")
cat("- Concentration at 15 min:", drug_data$Concentration[2], "ppb\n")
cat("- Concentration at 30 min:", drug_data$Concentration[3], "ppb\n")
cat("- Concentration at 60 min:", drug_data$Concentration[4], "ppb\n")

# 计算浓度变化百分比 (基于峰值浓度728 ppb)
peak_conc <- max(drug_data$Concentration)  # 峰值浓度 728 ppb
cat("\nConcentration Changes (relative to peak at 728 ppb):\n")
for(i in 1:nrow(drug_data)) {
  if(drug_data$Concentration[i] == peak_conc) {
    cat("- At", drug_data$Time[i], "min: Peak concentration (", peak_conc, "ppb)\n")
  } else {
    change_percent <- round((peak_conc - drug_data$Concentration[i]) / peak_conc * 100, 1)
    cat("- At", drug_data$Time[i], "min: Decreased by", change_percent, "% from peak\n")
  }
}

cat("\nFigures saved in the following formats:\n")
cat("- PDF: SU3327_concentration_time_curve.pdf (Nature journal preferred)\n")
cat("- PNG: SU3327_concentration_time_curve.png (High-resolution bitmap)\n")
cat("- SVG: SU3327_concentration_time_curve.svg (Editable vector graphics)\n")
