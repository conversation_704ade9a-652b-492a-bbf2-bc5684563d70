# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-14
# 描述: 使用ChemmineR生成真实分子结构图 - 20个代表性分子骨架

# 清理环境
rm(list = ls())

# 加载必要的包
suppressMessages({
    library(ChemmineR)
    library(ggplot2)
    library(dplyr)
    library(readr)
    library(gridExtra)
    library(grid)
    library(grDevices)
})

# 设置随机种子
set.seed(42)

cat("=== ChemmineR分子结构可视化 ===\n")

# 读取分子骨架数据
scaffold_data <- read_csv("01_原始数据/分子骨架数据/scaffold_smiles_data.csv", 
                         show_col_types = FALSE)

cat("数据加载完成，共", nrow(scaffold_data), "个分子骨架\n")

# 中国风学术配色
chinese_colors <- c("#C0392B", "#2980B9", "#27AE60", "#F1C40F", 
                   "#A0522D", "#34495E", "#73C6B6", "#E74C3C")

# 使用ChemmineR处理SMILES
process_smiles_with_chemminer <- function(smiles_vector, ids) {
    cat("使用ChemmineR处理SMILES结构...\n")
    
    # 创建SDF对象
    valid_molecules <- list()
    valid_ids <- character()
    
    for (i in seq_along(smiles_vector)) {
        tryCatch({
            # 将SMILES转换为SDF格式
            sdf_mol <- smiles2sdf(smiles_vector[i])
            
            if (length(sdf_mol) > 0) {
                valid_molecules[[length(valid_molecules) + 1]] <- sdf_mol[[1]]
                valid_ids <- c(valid_ids, ids[i])
                cat("  ✓", ids[i], "处理成功\n")
            }
        }, error = function(e) {
            cat("  ✗", ids[i], "处理失败:", e$message, "\n")
        })
    }
    
    if (length(valid_molecules) > 0) {
        # 创建SDFset对象
        sdfset <- SDFset(valid_molecules)
        names(sdfset) <- valid_ids
        return(sdfset)
    } else {
        return(NULL)
    }
}

# 处理SMILES数据
sdfset <- process_smiles_with_chemminer(scaffold_data$SMILES, scaffold_data$Scaffold_ID)

if (!is.null(sdfset)) {
    cat("成功处理", length(sdfset), "个分子结构\n")
    
    # 创建输出目录
    output_dir <- "03_图表输出/论文级图表"
    if (!dir.exists(output_dir)) {
        dir.create(output_dir, recursive = TRUE)
    }
    
    # 生成分子结构图 - 网格布局
    cat("生成分子结构网格图...\n")
    
    # 计算网格布局 (4x5)
    n_molecules <- length(sdfset)
    n_cols <- 4
    n_rows <- ceiling(n_molecules / n_cols)
    
    # 创建高质量PNG图片
    png_file <- file.path(output_dir, "分子骨架真实结构图_ChemmineR.png")
    
    # 设置图片参数
    png(png_file, width = 16, height = 20, units = "in", res = 300, bg = "white")
    
    # 设置图形参数
    par(mfrow = c(n_rows, n_cols), 
        mar = c(2, 1, 3, 1),  # 边距: 下, 左, 上, 右
        oma = c(4, 2, 4, 2),  # 外边距
        bg = "#FDFEFE")       # 背景色
    
    # 绘制每个分子结构
    for (i in 1:n_molecules) {
        tryCatch({
            # 绘制分子结构
            plot(sdfset[i], 
                 print = FALSE,
                 main = names(sdfset)[i],
                 cex.main = 1.2,
                 col.main = "#34495E",
                 font.main = 2)
            
            # 添加SMILES信息
            smiles_text <- scaffold_data$SMILES[scaffold_data$Scaffold_ID == names(sdfset)[i]]
            if (length(smiles_text) > 0 && nchar(smiles_text) > 30) {
                smiles_text <- paste0(substr(smiles_text, 1, 30), "...")
            }
            
            mtext(smiles_text, 
                  side = 1, 
                  line = 0.5, 
                  cex = 0.7, 
                  col = "#34495E")
            
        }, error = function(e) {
            # 如果绘制失败，显示错误信息
            plot.new()
            text(0.5, 0.5, paste("Error:", names(sdfset)[i]), 
                 cex = 1, col = "red", adj = c(0.5, 0.5))
        })
    }
    
    # 填充空白位置
    remaining_plots <- (n_rows * n_cols) - n_molecules
    if (remaining_plots > 0) {
        for (j in 1:remaining_plots) {
            plot.new()
        }
    }
    
    # 添加总标题
    mtext("Representative Molecular Scaffolds - Chemical Structure Analysis", 
          outer = TRUE, 
          cex = 1.8, 
          font = 2, 
          col = "#34495E",
          line = 1)
    
    mtext("20 Key Scaffolds from Chemical Database | Generated with ChemmineR", 
          outer = TRUE, 
          cex = 1.2, 
          col = "#34495E",
          line = -1,
          side = 1)
    
    # 关闭图形设备
    dev.off()
    
    cat("ChemmineR分子结构图保存完成:", png_file, "\n")
    
    # 生成PDF版本
    pdf_file <- file.path(output_dir, "分子骨架真实结构图_ChemmineR.pdf")
    
    pdf(pdf_file, width = 16, height = 20, bg = "white")
    
    par(mfrow = c(n_rows, n_cols), 
        mar = c(2, 1, 3, 1),
        oma = c(4, 2, 4, 2),
        bg = "#FDFEFE")
    
    # 重复绘制过程（PDF版本）
    for (i in 1:n_molecules) {
        tryCatch({
            plot(sdfset[i], 
                 print = FALSE,
                 main = names(sdfset)[i],
                 cex.main = 1.2,
                 col.main = "#34495E",
                 font.main = 2)
            
            smiles_text <- scaffold_data$SMILES[scaffold_data$Scaffold_ID == names(sdfset)[i]]
            if (length(smiles_text) > 0 && nchar(smiles_text) > 30) {
                smiles_text <- paste0(substr(smiles_text, 1, 30), "...")
            }
            
            mtext(smiles_text, 
                  side = 1, 
                  line = 0.5, 
                  cex = 0.7, 
                  col = "#34495E")
            
        }, error = function(e) {
            plot.new()
            text(0.5, 0.5, paste("Error:", names(sdfset)[i]), 
                 cex = 1, col = "red", adj = c(0.5, 0.5))
        })
    }
    
    # 填充空白位置
    if (remaining_plots > 0) {
        for (j in 1:remaining_plots) {
            plot.new()
        }
    }
    
    # 添加标题
    mtext("Representative Molecular Scaffolds - Chemical Structure Analysis", 
          outer = TRUE, 
          cex = 1.8, 
          font = 2, 
          col = "#34495E",
          line = 1)
    
    mtext("20 Key Scaffolds from Chemical Database | Generated with ChemmineR", 
          outer = TRUE, 
          cex = 1.2, 
          col = "#34495E",
          line = -1,
          side = 1)
    
    dev.off()
    
    cat("PDF版本保存完成:", pdf_file, "\n")
    
} else {
    cat("⚠ 无法处理任何SMILES结构，请检查数据格式\n")
}

cat("\n=== ChemmineR分子结构可视化完成 ===\n")
cat("生成的文件:\n")
cat("- PNG格式: 03_图表输出/论文级图表/分子骨架真实结构图_ChemmineR.png\n")
cat("- PDF格式: 03_图表输出/论文级图表/分子骨架真实结构图_ChemmineR.pdf\n")
cat("特点:\n")
cat("- 真实的2D分子结构图\n")
cat("- 4x5网格布局\n")
cat("- Nature期刊风格设计\n")
cat("- 高分辨率输出(300 DPI)\n")
