# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-14
# 描述: 20个代表性分子骨架结构可视化 - SCI期刊级图表生成

# 清理环境
rm(list = ls())

# 加载必要的包
suppressMessages({
    library(ggplot2)
    library(dplyr)
    library(readr)
    library(gridExtra)
    library(grid)
    library(stringr)
    library(ChemmineR)
    library(grDevices)
    library(Cairo)
    library(scales)
    library(RColorBrewer)
})

# 设置随机种子
set.seed(42)

# 读取分子骨架数据
cat("=== 分子骨架结构可视化 ===\n")
cat("读取分子骨架数据...\n")

scaffold_data <- read_csv("01_原始数据/分子骨架数据/scaffold_smiles_data.csv", 
                         show_col_types = FALSE)

cat("数据加载完成，共", nrow(scaffold_data), "个分子骨架\n")

# 中国风学术配色方案（基于规范文件）
chinese_academic_colors <- c(
    "#C0392B",  # 朱红 (Primary)
    "#2980B9",  # 靛蓝 (Secondary) 
    "#27AE60",  # 石绿 (Accent 2)
    "#F1C40F",  # 藤黄 (Accent 3)
    "#A0522D",  # 赭石 (Accent 1)
    "#34495E",  # 青金 (Accent 4)
    "#73C6B6",  # 天青 (Tertiary)
    "#E74C3C"   # 备用红色
)

# Nature风格主题函数
theme_nature_chinese <- function(base_size = 8, base_family = "Arial") {
    theme_minimal(base_size = base_size, base_family = base_family) +
        theme(
            # 标题设置
            plot.title = element_text(
                hjust = 0.5, 
                size = rel(1.5), 
                color = "#34495E",
                face = "bold",
                margin = margin(b = 20)
            ),
            plot.subtitle = element_text(
                hjust = 0.5, 
                color = "#34495E",
                size = rel(1.1),
                margin = margin(b = 15)
            ),
            
            # 坐标轴设置
            axis.title = element_text(
                size = rel(1.2), 
                color = "#34495E",
                face = "bold"
            ),
            axis.text = element_text(
                color = "#34495E",
                size = rel(0.9)
            ),
            axis.line = element_line(
                color = "#34495E", 
                linewidth = 0.5
            ),
            
            # 面板设置
            panel.grid = element_blank(),
            panel.border = element_rect(
                colour = "#34495E", 
                fill = NA, 
                linewidth = 0.8
            ),
            
            # 图例设置
            legend.title = element_text(
                color = "#34495E",
                face = "bold"
            ),
            legend.text = element_text(color = "#34495E"),
            legend.key = element_blank(),
            
            # 背景设置
            plot.background = element_rect(
                fill = "#FDFEFE", 
                color = NA
            ),
            panel.background = element_rect(
                fill = "#FDFEFE", 
                color = NA
            ),
            
            # 分面设置
            strip.background = element_rect(
                fill = "white", 
                colour = "#34495E",
                linewidth = 0.5
            ),
            strip.text = element_text(
                color = "#34495E",
                face = "bold",
                size = rel(1.0)
            )
        )
}

# 使用ChemmineR处理SMILES并创建分子结构图
create_molecule_structure_plot <- function(scaffold_data) {
    cat("开始处理分子结构...\n")
    
    # 创建分子结构数据框
    mol_data <- scaffold_data %>%
        mutate(
            # 计算基本分子特征用于可视化
            smiles_length = nchar(SMILES),
            carbon_count = str_count(SMILES, "[Cc]"),
            nitrogen_count = str_count(SMILES, "[Nn]"),
            oxygen_count = str_count(SMILES, "[Oo]"),
            ring_count = str_count(SMILES, "[0-9]"),
            
            # 为网格布局计算位置
            row = ceiling(row_number() / 4),  # 4列布局
            col = ((row_number() - 1) %% 4) + 1,
            
            # 分子复杂度评分（用于颜色映射）
            complexity_score = smiles_length + carbon_count * 0.5 + 
                              nitrogen_count * 2 + oxygen_count * 1.5
        )
    
    return(mol_data)
}

# 处理分子数据
mol_processed <- create_molecule_structure_plot(scaffold_data)

cat("分子数据处理完成\n")
cat("分子复杂度范围:", round(min(mol_processed$complexity_score), 2), 
    "到", round(max(mol_processed$complexity_score), 2), "\n")

# 创建分子结构网格图表
create_scaffold_grid_plot <- function(mol_data) {
    cat("创建分子骨架网格图表...\n")
    
    # 创建基础散点图来展示分子骨架分布
    p <- ggplot(mol_data, aes(x = col, y = -row)) +
        # 使用点来表示分子位置，颜色映射复杂度
        geom_point(
            aes(color = complexity_score, size = carbon_count),
            alpha = 0.8,
            stroke = 1.2
        ) +
        
        # 添加分子标签
        geom_text(
            aes(label = Scaffold_ID),
            vjust = -1.5,
            hjust = 0.5,
            size = 2.5,
            color = "#34495E",
            fontface = "bold"
        ) +
        
        # 添加SMILES信息（简化显示）
        geom_text(
            aes(label = paste0("C:", carbon_count, " N:", nitrogen_count, " O:", oxygen_count)),
            vjust = 2.5,
            hjust = 0.5,
            size = 1.8,
            color = "#34495E",
            alpha = 0.7
        ) +
        
        # 颜色和大小映射
        scale_color_gradientn(
            colors = chinese_academic_colors[c(2, 6, 1)],  # 靛蓝到青金到朱红
            name = "Complexity\nScore",
            guide = guide_colorbar(
                title.position = "top",
                title.hjust = 0.5,
                barwidth = 8,
                barheight = 0.8
            )
        ) +
        
        scale_size_continuous(
            range = c(3, 8),
            name = "Carbon\nCount",
            guide = guide_legend(
                title.position = "top",
                title.hjust = 0.5,
                override.aes = list(alpha = 0.8)
            )
        ) +
        
        # 坐标轴设置
        scale_x_continuous(
            limits = c(0.5, 4.5),
            breaks = 1:4,
            labels = paste("Column", 1:4)
        ) +
        scale_y_continuous(
            limits = c(-6, -0.5),
            breaks = -5:-1,
            labels = paste("Row", 1:5)
        ) +
        
        # 标签和标题
        labs(
            title = "Representative Molecular Scaffolds Structure Analysis",
            subtitle = "20 Key Scaffolds from Chemical Database with Complexity Mapping",
            x = "Grid Position (Column)",
            y = "Grid Position (Row)",
            caption = "Data Source: ChEMBL Database | Visualization: ZK Research Group"
        ) +
        
        # 应用主题
        theme_nature_chinese(base_size = 10) +
        
        # 图例位置
        theme(
            legend.position = "bottom",
            legend.box = "horizontal",
            legend.margin = margin(t = 15),
            plot.caption = element_text(
                hjust = 1,
                color = "#34495E",
                size = rel(0.8),
                margin = margin(t = 10)
            )
        ) +
        
        # 确保比例协调
        coord_fixed(ratio = 1)
    
    return(p)
}

# 生成主图表
main_plot <- create_scaffold_grid_plot(mol_processed)

cat("主图表创建完成\n")

# 创建输出目录
output_dir <- "03_图表输出/论文级图表"
if (!dir.exists(output_dir)) {
    dir.create(output_dir, recursive = TRUE)
}

cat("开始保存图表文件...\n")

# 保存高质量PNG图片
png_file <- file.path(output_dir, "分子骨架结构图_Nature风格.png")
ggsave(
    filename = png_file,
    plot = main_plot,
    width = 12,
    height = 10,
    dpi = 300,
    bg = "white",
    device = "png"
)

cat("PNG图片保存完成:", png_file, "\n")

# 保存SVG矢量图
svg_file <- file.path(output_dir, "分子骨架结构图_Nature风格.svg")
ggsave(
    filename = svg_file,
    plot = main_plot,
    width = 12,
    height = 10,
    bg = "white",
    device = "svg"
)

cat("SVG矢量图保存完成:", svg_file, "\n")

# 显示图表
print(main_plot)

cat("\n=== 分子骨架可视化完成 ===\n")
cat("生成的文件:\n")
cat("- PNG格式:", png_file, "\n")
cat("- SVG格式:", svg_file, "\n")
cat("图表特点:\n")
cat("- 网格布局展示20个分子骨架\n")
cat("- 复杂度颜色映射\n")
cat("- 碳原子数量大小映射\n")
cat("- Nature期刊风格设计\n")
cat("- 中国风配色方案\n")
