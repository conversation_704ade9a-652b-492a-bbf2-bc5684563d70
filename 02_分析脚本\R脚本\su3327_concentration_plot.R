
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2024-07-22
# 描述: 绘制SU3327浓度随时间下降的折线图，符合Nature期刊风格

library(ggplot2)

# 定义Nature主题函数
theme_nature <- function(base_size = 7, base_family = "Arial") {
  theme_bw(base_size = base_size, base_family = base_family) +
  theme(
    panel.grid.major = element_blank(),
    panel.grid.minor = element_blank(),
    panel.border = element_rect(colour = "black", fill=NA, linewidth=0.5),
    plot.title = element_text(hjust = 0.5, size = rel(1.1)),
    axis.text = element_text(color = "black"),
    axis.ticks = element_line(colour = "black", linewidth = 0.5),
    legend.key = element_blank()
  )
}

# Wong配色方案（选择一种蓝色作为线条颜色）
wong_blue <- "#0072B2"

# 数据（假设浓度从728 ppb下降到118 ppb，最后点为0以匹配向下趋势；如需调整请修改）
data <- data.frame(
  Time = c(0, 15, 30, 60),
  Concentration = c(728, 393, 118, 0)
)

# 绘制折线图
p <- ggplot(data, aes(x = Time, y = Concentration)) +
  geom_line(color = wong_blue, linewidth = 1) +
  geom_point(color = wong_blue, size = 2) +
  labs(
    x = "Time (min)",
    y = "SU3327 Concentration (ppb)",
    title = "SU3327 Concentration Decline Over Time"
  ) +
  theme_nature()

# 保存为PDF和PNG（单栏尺寸：89 mm宽）
# 修改输出目录为项目根下的相对路径
output_dir <- "03_图表输出/论文级图表/"
ggsave(file.path(output_dir, "su3327_concentration_decline.pdf"), p, width = 89/25.4, height = 60/25.4, units = "in", device = cairo_pdf)
ggsave(file.path(output_dir, "su3327_concentration_decline.png"), p, width = 89/25.4, height = 60/25.4, units = "in", dpi = 300) 