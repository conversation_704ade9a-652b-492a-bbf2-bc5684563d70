---
description:
globs:
alwaysApply: false
---
name: 项目目标与分析流程
description: 定义项目的核心目标、标准数据分析工作流以及对新数据源的自适应要求。
---
# 项目核心目标与分析流程

## 1. 核心目标
本项目的核心目标是对用户提供的数据集进行深入、严谨的科学分析。所有工作都应以生成可发表于顶级学术期刊（如 Nature）为最终标准。具体任务包括：
- 利用R语言进行全面的数据探索、统计分析和建模。
- 创建符合 **Nature期刊标准** 和 **中国古典风格** 的高质量、信息丰富的图表。
- 撰写结构清晰、论证严谨、格式规范的学术级数据分析报告。

## 2. 标准工作流
所有分析任务应遵循以下标准流程，以确保系统性和可复现性：
1.  **数据加载与自动识别**:
    - **首要任务**：自动扫描并识别 [模式数据](mdc:模式数据) 目录下的所有数据文件（如 `.csv`, `.tsv`, `.xlsx`）。
    - **适应性**: 流程必须能够灵活处理新增加的数据文件，无需手动修改代码。
2.  **数据清洗与预处理**:
    - 根据数据特点，执行必要的清洗步骤，如处理缺失值（Missing Values）、异常值（Outliers）、修正数据类型等。
3.  **探索性数据分析 (Exploratory Data Analysis, EDA)**:
    - 计算核心描述性统计量（均值、中位数、标准差等）。
    - 分析关键变量的数据分布、相关性，并利用初步的可视化进行探索。
4.  **建模与统计分析**:
    - 基于前期探索和具体研究问题，选择并应用恰当的统计模型或机器学习算法。
    - 在报告的“方法”部分清晰阐述选择该模型的理由。
5.  **专业级可视化**:
    - 使用R语言的 `ggplot2` 等专业绘图包创建图表。
    - 严格遵守 [Nature风格图表规范](mdc:.cursor/rules/Nature风格图表规范.mdc) 和 [中国风图表规范](mdc:.cursor/rules/中国风图表规范.mdc) 中的所有细则。
6.  **学术报告生成**:
    - 将所有分析步骤、结果（图表）和结论，系统地整合到一份Markdown格式的学术报告中。
    - 报告的撰写必须遵循 [学术报告撰写规范](mdc:.cursor/rules/学术报告撰写规范.mdc)。

## 3. 灵活性与自动化
本项目的灵魂在于自动化和高适应性。AI应持续优化工作流程，以最少的人工干预，完成从数据读取到报告生成的全过程。当被问及“分析数据”或类似请求时，应主动遵循此完整流程。
